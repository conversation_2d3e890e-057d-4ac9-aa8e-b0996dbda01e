import { Group } from "fabric";
import { IImage } from "../../base/package/image/image";

export function getContainerBounds(extendContainer: Group) {
    const width = extendContainer.getScaledWidth()
    const height = extendContainer.getScaledHeight()
    const left = extendContainer.getCenterPoint().x - width / 2
    const top = extendContainer.getCenterPoint().y - height / 2
    return {
        left,
        top,
        right: left + width,
        bottom: top + height,
    }
}

export function getTargetBounds(target: IImage) {
    const width = target.getScaledWidth()
    const height = target.getScaledHeight()
    const left = target.getCenterPoint().x - width / 2
    const top = target.getCenterPoint().y - height / 2
    return {
        left,
        top,
        right: left + width,
        bottom: top + height,
    }
}
// 缩放目标图片
export const cornerBoundsHandler: Record<string, (target: IImage, extendContainer: Group) => {
    maxScaleX: number
    maxScaleY: number
    left: number
    top: number
}> = {
    'tr': (target, extendContainer) => {
        // 原点设置为左下角
        const { right, top } = getContainerBounds(extendContainer)
        const { width, height, left: targetLeft, top: targetTop } = target
        const maxScaleX = (right - targetLeft) / width
        const maxScaleY = (targetTop - top) / height
        const selfLeft = target.getCenterPoint().x - target.getScaledWidth() / 2
        const selfTop = target.getCenterPoint().y + target.getScaledHeight() / 2
        return {
            maxScaleX,
            maxScaleY,
            left: selfLeft,
            top: selfTop,
        }
    },
    'tl': (target, extendContainer) => {
        // 原点设置为右下角
        const { left, top } = getContainerBounds(extendContainer)
        const { width, height, left: targetLeft, top: targetTop } = target
        const maxScaleX = (targetLeft - left) / width
        const maxScaleY = (targetTop - top) / height
        const selfLeft = target.getCenterPoint().x + target.getScaledWidth() / 2
        const selfTop = target.getCenterPoint().y + target.getScaledHeight() / 2
        return {
            maxScaleX,
            maxScaleY,
            left: selfLeft,
            top: selfTop,
        }
    },
    'br': (target, extendContainer) => {
        // 原点设置为左上角
        const { right, bottom } = getContainerBounds(extendContainer)
        const { width, height, left: targetLeft, top: targetTop } = target
        const maxScaleX = (right - targetLeft) / width
        const maxScaleY = (bottom - targetTop) / height
        const selfLeft = target.getCenterPoint().x - target.getScaledWidth() / 2
        const selfTop = target.getCenterPoint().y - target.getScaledHeight() / 2
        return {
            maxScaleX,
            maxScaleY,
            left: selfLeft,
            top: selfTop,
        }
    },
    'bl': (target, extendContainer) => {
        // 原点设置为右上角
        const { bottom, left } = getContainerBounds(extendContainer)
        const { width, height, left: targetLeft, top: targetTop } = target
        const maxScaleX = (targetLeft - left) / width
        const maxScaleY = (bottom - targetTop ) / height
        const selfLeft = target.getCenterPoint().x + target.getScaledWidth() / 2
        const selfTop = target.getCenterPoint().y - target.getScaledHeight() / 2
        return {
            maxScaleX,
            maxScaleY,
            left: selfLeft,
            top: selfTop,
        }
    }
}

// 缩放扩展容器
export const extendContainerCornerBoundsHandler: Record<string, (target: IImage, extendContainer: Group) => {
    maxScaleY?: number
    minScaleY?: number
    maxScaleX?: number
    minScaleX?:number
    left: number
    top: number
}> = {
    'mt': (target, extendContainer) => {
        const { top } = getTargetBounds(target)
        const { bottom: containerBottom } = getContainerBounds(extendContainer)

        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const maxScale = maxHeight / extendContainer.height
        const minScale = Math.max(Math.abs(top - containerBottom) / extendContainer.height, minHeight / extendContainer.height)
        const extendContainerTop = extendContainer.getCenterPoint().y + extendContainer.getScaledHeight() / 2
        return {
            maxScaleY: maxScale,
            minScaleY: minScale,
            left: extendContainer.getCenterPoint().x,
            top: extendContainerTop,
            originKeyX: 'center',
            originKeyY: 'bottom'
        }

    },
    'mb': (target, extendContainer) => {
        const { bottom } = getTargetBounds(target)
        const {  top: containerTop } = getContainerBounds(extendContainer)
        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const minScale = Math.max(Math.abs(bottom - containerTop) / extendContainer.height, minHeight / extendContainer.height)
        const maxScale = maxHeight / extendContainer.height
        const extendContainerTop = extendContainer.getCenterPoint().y - extendContainer.getScaledHeight() / 2
        return {
            maxScaleY: maxScale,
            minScaleY: minScale,
            left: extendContainer.getCenterPoint().x,
            top: extendContainerTop,
            originKeyX: 'center',
            originKeyY: 'top'
        }
    },
    'mr': (target, extendContainer) => {
        const { right } = getTargetBounds(target)
        const { left: containerLeft } = getContainerBounds(extendContainer)
        const { width } = extendContainer
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((right - containerLeft) / width, minWidth / width)
        return {
            maxScaleX,
            minScaleX,
            left: containerLeft,
            top: extendContainer.getCenterPoint().y,
            originKeyX: 'left',
            originKeyY: 'center'
        }
    },
    'ml': (target, extendContainer) => {
        const { left } = getTargetBounds(target)
        const { right: containerRight } = getContainerBounds(extendContainer)
        const { width } = extendContainer
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((containerRight - left) / width, minWidth / width)
        return {
            maxScaleX,
            minScaleX,
            left: containerRight,
            top: extendContainer.getCenterPoint().y,
            originKeyX: 'right',
            originKeyY: 'center'
        }
    },
    'tr': (target, extendContainer) => {
        const { top, right } = getTargetBounds(target)
        const { left: containerLeft, bottom: containerBottom } = getContainerBounds(extendContainer)

        const { width, height } = extendContainer
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((right - containerLeft) / width, minWidth / width)
        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const maxScaleY = maxHeight / height
        const minScaleY = Math.max(Math.abs(top - containerBottom) / height, minHeight / height)
        return {
            maxScaleX,
            minScaleX,
            maxScaleY,
            minScaleY,
            left: containerLeft,
            top: containerBottom,
            originKeyX: 'left',
            originKeyY: 'bottom'
        }
    },
    'tl': (target, extendContainer) => {
        const { top, left } = getTargetBounds(target)
        const { right: containerRight, bottom: containerBottom } = getContainerBounds(extendContainer)
        const { width, height } = extendContainer
        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const maxScaleY = maxHeight / height
        const minScaleY = Math.max(Math.abs(top - containerBottom) / height, minHeight / height)
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((containerRight - left) / width, minWidth / width)
        return {
            maxScaleY,
            minScaleY,
            maxScaleX,
            minScaleX,
            left: containerRight,
            top: containerBottom,
            originKeyX: 'right',
            originKeyY: 'bottom'
        }
    },
    'br': (target, extendContainer) => {
        const { bottom, right } = getTargetBounds(target)
        const { top: containerTop, left: containerLeft } = getContainerBounds(extendContainer)
        const { width, height } = extendContainer
        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const maxScaleY = maxHeight / height
        const minScaleY = Math.max(Math.abs(bottom - containerTop) / height, minHeight / height)
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((right - containerLeft) / width, minWidth / width)
        return {
            maxScaleY,
            minScaleY,
            maxScaleX,
            minScaleX,
            left: containerLeft,
            top: containerTop,
            originKeyX: 'left',
            originKeyY: 'top'
        }
    },
    'bl': (target, extendContainer) => {
        const { bottom, left } = getTargetBounds(target)
        const { top: containerTop, right: containerRight } = getContainerBounds(extendContainer)
        const { width, height } = extendContainer
        const maxHeight = extendContainer.get('maxExtendHeight')
        const minHeight = extendContainer.get('minExtendHeight')
        const maxScaleY = maxHeight / height
        const minScaleY = Math.max(Math.abs(bottom - containerTop) / height, minHeight / height)
        const maxWidth = extendContainer.get('maxExtendWidth')
        const minWidth = extendContainer.get('minExtendWidth')
        const maxScaleX = maxWidth / width
        const minScaleX = Math.max((containerRight - left) / width, minWidth / width)
        return {
            maxScaleY,
            minScaleY,
            maxScaleX,
            minScaleX,
            left: containerRight,
            top: containerTop,
            originKeyX: 'right',
            originKeyY: 'top'
        }
    }
}
