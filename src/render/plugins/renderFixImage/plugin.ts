import { z } from "zod"
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { fixImageOptionsSchema } from "./schema"
import { IImage } from '../../base/package/image/image';
import { renderMask, viewportTranslateHandler, viewportZoomHandler } from "../utils/mask";
import { BasicTransformEvent, Canvas, Group, Point, Rect, TPointerEvent, TPointerEventInfo } from "fabric";
import { TStartPoint } from "../renderSelection/types";
import { getTargetBounds } from "../utils/fixRect";


type OptionsParams = z.infer<typeof fixImageOptionsSchema>
export class FixImagePlugin extends Plugin {
    public __name__ = 'FixImagePlugin'
    public options: OptionsParams;
    private render: Render;
    private maskElement: Rect | undefined;

    private _viewportTranslateHandler;
    private _viewportZoomHandler;
    private _fixTarget: IImage

    private targetParent: Group | Canvas
    private targetParentIndex: number

    private _isMouseDown = false
    private _scenePoint: {
        start: Point;
        width: number;
        height: number;
    } = {
            start: new Point(0, 0),
            width: 0,
            height: 0,
        };
    private _startPoint: TStartPoint = { x: 0, y: 0 };
    private _fixAreas: Group[] = []

    constructor(render: Render, options: OptionsParams & { target: IImage }) {
        super(render);

        const result = fixImageOptionsSchema.parse(options);
        this.options = result;
        this.render = render
        this.render._FC.set('isDisabledContextMenu', true)
        this.render._FC.discardActiveObject()
        this._fixTarget = options.target
        this.targetParent = this._fixTarget.parent || this.render._FC
        this.targetParentIndex = this.targetParent._objects.indexOf(this._fixTarget)
        if (this.options.renderMask) {
            this.maskElement = renderMask.call(render, {
                maskColor: this.options.renderMaskColor,
                maskOpacity: this.options.renderMaskOpacity,
            });
            this._viewportTranslateHandler = viewportTranslateHandler.bind(this.maskElement, render);
            this._viewportZoomHandler = viewportZoomHandler.bind(this.maskElement, render);
            render._FC.on('viewport:translate', this._viewportTranslateHandler);
            render._FC.on('viewport:zoom', this._viewportZoomHandler);
        }

        // 从原有父级中移除
        this.targetParent._objects.splice(this.targetParentIndex, 1)
        // 添加到画布中最上级
        this.render._FC._objects.push(this._fixTarget)

        // 确保初始化时的正确层级顺序
        this._ensureCorrectLayerOrder()

        this.render._FC.renderAll()
        this._bindEvent()
        this._render._FC.selection = false;
        this.render._lockAll()

        // 初始化绘制状态
        this._resetDrawingState()
    }

    private _bindEvent = () => {
        this.render._FC.on('object:added', this._ensureCorrectLayerOrder)
        this.render._FC.on('mouse:down', this._mouseDownHandler)
        this.render._FC.on('mouse:move', this._mouseMoveHandler)
        this.render._FC.on('mouse:up', this._mouseUpHandler)
    }

    private _unBindEvent = () => {
        this.render._FC.off('object:added', this._ensureCorrectLayerOrder)
        this.render._FC.off('mouse:down', this._mouseDownHandler)
        this.render._FC.off('mouse:move', this._mouseMoveHandler)
        this.render._FC.off('mouse:up', this._mouseUpHandler)
    }

    /**
     * 重置绘制状态
     */
    private _resetDrawingState = () => {
        this._startPoint = { x: 0, y: 0 };
        this._scenePoint = {
            start: new Point(0, 0),
            width: 0,
            height: 0,
        };
    }

    private _mouseDownHandler = (e: TPointerEventInfo<TPointerEvent>) => {
        // 确保之前的绘制状态被重置
        if (this._isMouseDown) {
            this._isMouseDown = false;
            this._resetDrawingState();
            this._render._FC.clearContext(this._render._FC.contextTop);
        }

        // 如果点击的是目标图片，不触发绘制
        if (e.target === this._fixTarget) return;

        // 如果点击的是已存在的修复区域，不触发绘制新图形
        if (e.target && this._fixAreas.includes(e.target as Group)) return;

        // 开始新的绘制
        this._isMouseDown = true;
        this._scenePoint.start = this._render._FC.getScenePoint(e.e);
        const pointer = this._render._FC
            .getScenePoint(e.e)
            .transform(this._render._FC.viewportTransform);
        this._startPoint = {
            x: pointer.x,
            y: pointer.y,
        };
        this._render._FC.renderAll();
    }

    private _mouseMoveHandler = (e: TPointerEventInfo<TPointerEvent>) => {
        if (!this._isMouseDown) return;
        this._scenePoint.width =
            this._render._FC.getScenePoint(e.e).x - this._scenePoint.start.x;
        this._scenePoint.height =
            this._render._FC.getScenePoint(e.e).y - this._scenePoint.start.y;
        const pointer = this._render._FC
            .getScenePoint(e.e)
            .transform(this._render._FC.viewportTransform);
        const width = pointer.x - this._startPoint.x;
        const height = pointer.y - this._startPoint.y;

        this.draw(this._startPoint.x, this._startPoint.y, width, height);
    }

    private draw = (x: number, y: number, width: number, height: number) => {
        const ctx = this._render._FC.contextTop;
        const maxWidth = this.options.maxFixAreaWidth * this._render._FC.getZoom()
        const maxHeight = this.options.maxFixAreaHeight * this._render._FC.getZoom()
        const minWidth = this.options.minFixAreaWidth * this._render._FC.getZoom()
        const minHeight = this.options.minFixAreaHeight * this._render._FC.getZoom()
        if (
            Math.abs(width) < minWidth && Math.abs(height) < minHeight ||
            Math.abs(width) > maxWidth && Math.abs(height) > maxHeight
        ) return
        this._render._FC.clearContext(ctx);
        const flipX = width > 0 ? 1 : -1
        const flipY = height > 0 ? 1 : -1
        const renderWidth = Math.min(Math.max(Math.abs(width), minWidth), maxWidth)
        const renderHeight = Math.min(Math.max(Math.abs(height), minHeight), maxHeight)
        ctx.save();
        ctx.beginPath();
        ctx.rect(x, y, renderWidth * flipX, renderHeight * flipY);
        ctx.fillStyle = this.options.fixAreaTemporaryColor;
        ctx.globalAlpha = this.options.fixAreaTemporaryOpacity;
        ctx.fill();
        ctx.restore();
    };

    private _mouseUpHandler = () => {
        if (!this._isMouseDown) return;
        if (!this._startPoint) return;

        const ctx = this._render._FC.contextTop;
        this._render._FC.clearContext(ctx);
        const { width, height, start } = this._scenePoint;

        // 重置状态（无论是否创建修复区域都要重置）
        this._isMouseDown = false;
        this._resetDrawingState();

        // 检查绘制尺寸是否有效（宽或高任何一边小于10像素认为无效）
        if (Math.abs(width) < 10 || Math.abs(height) < 10) {
            return
        }

        let targetWidth = 0
        let targetHeight = 0
        if (width === 0 && height === 0) {
            targetWidth = this.options.minFixAreaWidth
            targetHeight = this.options.minFixAreaHeight
        } else {
            targetWidth = Math.min(Math.max(Math.abs(width), this.options.minFixAreaWidth), this.options.maxFixAreaWidth)
            targetHeight = Math.min(Math.max(Math.abs(height), this.options.minFixAreaHeight), this.options.maxFixAreaHeight)
        }

        // 计算绘制区域的边界（基于鼠标拖拽）
        const flipX = width >= 0 ? 1 : -1
        const flipY = height >= 0 ? 1 : -1
        let drawLeft = start.x
        let drawTop = start.y
        let drawRight = start.x + targetWidth * flipX
        let drawBottom = start.y + targetHeight * flipY

        // 确保left < right, top < bottom
        if (drawLeft > drawRight) {
            [drawLeft, drawRight] = [drawRight, drawLeft]
        }
        if (drawTop > drawBottom) {
            [drawTop, drawBottom] = [drawBottom, drawTop]
        }

        // 获取图片边界
        const targetBounds = getTargetBounds.call(this._fixTarget)

        // 检查绘制区域是否与图片区域有交集
        const hasIntersection = !(drawRight <= targetBounds.left ||
                                 drawLeft >= targetBounds.right ||
                                 drawBottom <= targetBounds.top ||
                                 drawTop >= targetBounds.bottom)

        // 如果绘制区域完全在图片外部，则认为是无效绘制
        if (!hasIntersection) {
            return
        }

        // 裁剪到图片区域内，而不是移动位置
        const clippedLeft = Math.max(drawLeft, targetBounds.left)
        const clippedTop = Math.max(drawTop, targetBounds.top)
        const clippedRight = Math.min(drawRight, targetBounds.right)
        const clippedBottom = Math.min(drawBottom, targetBounds.bottom)

        // 计算裁剪后的尺寸
        const clippedWidth = clippedRight - clippedLeft
        const clippedHeight = clippedBottom - clippedTop

        // 如果裁剪后的区域太小，则不创建修复区域
        if (clippedWidth < this.options.minFixAreaWidth || clippedHeight < this.options.minFixAreaHeight) {
            return
        }

        // 计算裁剪后区域的中心点
        const centerX = clippedLeft + clippedWidth / 2
        const centerY = clippedTop + clippedHeight / 2

        const fixArea = new Group([], {
            left: centerX,
            top: centerY,
            width: clippedWidth,
            height: clippedHeight,
            backgroundColor: this.options.fixAreaTemporaryColor,
            opacity: this.options.fixAreaTemporaryOpacity,
        })

        // 设置边界属性以确保缩放时的正确边界检查
        fixArea.set('maxWidth', this.options.maxFixAreaWidth)
        fixArea.set('maxHeight', this.options.maxFixAreaHeight)
        fixArea.set('minWidth', this.options.minFixAreaWidth)
        fixArea.set('minHeight', this.options.minFixAreaHeight)

        fixArea.on('moving', this._onMovingFixArea)
        fixArea.on('scaling', this._onScaleFixArea)
        fixArea.set('_name_', 'fixArea')

        // 如果已经达到最大数量，删除最先绘制的矩形
        if (this._fixAreas.length >= this.options.maxAreaCount) {
            const oldestFixArea = this._fixAreas.shift() // 移除数组中的第一个元素（最先绘制的）
            if (oldestFixArea) {
                this.render._FC.remove(oldestFixArea) // 从画布中移除
            }
        }

        this._fixAreas.push(fixArea)
        this.render._FC.add(fixArea)
        this.render._FC.bringObjectToFront(fixArea)
        this.render._FC.renderAll();
    }


    private _ensureCorrectLayerOrder = () => {
        if (this.options.renderMask && this.maskElement) {
            this.render._FC.bringObjectToFront(this.maskElement)
        }
        this.render._FC.bringObjectToFront(this._fixTarget)
    }

    private _onMovingFixArea = (e: BasicTransformEvent<TPointerEvent>) => {
        const fixArea = e.transform.target as Group
        const targetBounds = getTargetBounds.call(this._fixTarget)

        // 计算缩放后的实际尺寸
        const actualWidth = fixArea.width * fixArea.scaleX
        const actualHeight = fixArea.height * fixArea.scaleY
        const halfWidth = actualWidth / 2
        const halfHeight = actualHeight / 2

        // 计算允许的移动范围
        const minX = targetBounds.left + halfWidth
        const maxX = targetBounds.right - halfWidth
        const minY = targetBounds.top + halfHeight
        const maxY = targetBounds.bottom - halfHeight

        // 限制在图片范围内移动
        let newX = fixArea.left
        let newY = fixArea.top

        if (minX <= maxX) {
            newX = Math.max(minX, Math.min(maxX, fixArea.left))
        } else {
            // 如果fixArea比图片还宽，居中显示
            newX = (targetBounds.left + targetBounds.right) / 2
        }

        if (minY <= maxY) {
            newY = Math.max(minY, Math.min(maxY, fixArea.top))
        } else {
            // 如果fixArea比图片还高，居中显示
            newY = (targetBounds.top + targetBounds.bottom) / 2
        }

        fixArea.set({
            left: newX,
            top: newY
        })
        this.render._FC.renderAll()
    }

    private _onScaleFixArea = (e: BasicTransformEvent<TPointerEvent>) => {
        const fixArea = e.transform.target as Group
        const corner = e.transform.corner
        const targetBounds = getTargetBounds.call(this._fixTarget)

        // 计算当前fixArea的边界
        const currentWidth = fixArea.width * fixArea.scaleX
        const currentHeight = fixArea.height * fixArea.scaleY
        const currentLeft = fixArea.left - currentWidth / 2
        const currentTop = fixArea.top - currentHeight / 2
        const currentRight = fixArea.left + currentWidth / 2
        const currentBottom = fixArea.top + currentHeight / 2

        // 根据拖拽角落确定固定点和可变边界
        let fixedX: number, fixedY: number
        let maxNewWidth: number, maxNewHeight: number
        const minNewWidth = this.options.minFixAreaWidth
        const minNewHeight = this.options.minFixAreaHeight

        switch (corner) {
            case 'tl': // 左上角，固定右下角
                fixedX = currentRight
                fixedY = currentBottom
                maxNewWidth = Math.min(fixedX - targetBounds.left, this.options.maxFixAreaWidth)
                maxNewHeight = Math.min(fixedY - targetBounds.top, this.options.maxFixAreaHeight)
                break
            case 'tr': // 右上角，固定左下角
                fixedX = currentLeft
                fixedY = currentBottom
                maxNewWidth = Math.min(targetBounds.right - fixedX, this.options.maxFixAreaWidth)
                maxNewHeight = Math.min(fixedY - targetBounds.top, this.options.maxFixAreaHeight)
                break
            case 'bl': // 左下角，固定右上角
                fixedX = currentRight
                fixedY = currentTop
                maxNewWidth = Math.min(fixedX - targetBounds.left, this.options.maxFixAreaWidth)
                maxNewHeight = Math.min(targetBounds.bottom - fixedY, this.options.maxFixAreaHeight)
                break
            case 'br': // 右下角，固定左上角
                fixedX = currentLeft
                fixedY = currentTop
                maxNewWidth = Math.min(targetBounds.right - fixedX, this.options.maxFixAreaWidth)
                maxNewHeight = Math.min(targetBounds.bottom - fixedY, this.options.maxFixAreaHeight)
                break
            default:
                // 边缘拖拽，处理单方向缩放
                this._handleEdgeScaling(fixArea, corner, targetBounds)
                return
        }

        // 使用当前实际尺寸计算宽高比（保持当前状态的比例）
        const currentAspectRatio = currentWidth / currentHeight

        // 计算新的尺寸（基于当前缩放状态）
        const newWidth = fixArea.width * fixArea.scaleX
        const newHeight = fixArea.height * fixArea.scaleY

        // 对角缩放时，保持当前fixArea的实际宽高比
        // 计算缩放因子（取宽度和高度缩放的平均值，保持比例）
        const scaleFactorX = newWidth / currentWidth
        const scaleFactorY = newHeight / currentHeight
        const averageScaleFactor = (scaleFactorX + scaleFactorY) / 2

        // 应用平均缩放因子，保持当前比例
        let finalWidth = currentWidth * averageScaleFactor
        let finalHeight = currentHeight * averageScaleFactor

        // 应用边界限制
        finalWidth = Math.max(minNewWidth, Math.min(maxNewWidth, finalWidth))
        finalHeight = Math.max(minNewHeight, Math.min(maxNewHeight, finalHeight))

        // 如果边界限制破坏了比例，重新调整
        const constrainedAspectRatio = finalWidth / finalHeight
        if (Math.abs(constrainedAspectRatio - currentAspectRatio) > 0.01) {
            // 选择限制更严格的方向作为主导
            const widthLimitRatio = finalWidth / currentWidth
            const heightLimitRatio = finalHeight / currentHeight

            if (widthLimitRatio < heightLimitRatio) {
                // 宽度限制更严格，以宽度为准
                finalHeight = finalWidth / currentAspectRatio
            } else {
                // 高度限制更严格，以高度为准
                finalWidth = finalHeight * currentAspectRatio
            }
        }

        // 计算新的缩放比例
        const newScaleX = finalWidth / fixArea.width
        const newScaleY = finalHeight / fixArea.height

        // 确保角落缩放时保持等比例
        const finalScaleX = newScaleX
        const finalScaleY = newScaleY

        // 计算新的中心点位置（保持固定点不变）
        let newCenterX: number, newCenterY: number

        switch (corner) {
            case 'tl':
                newCenterX = fixedX - finalWidth / 2
                newCenterY = fixedY - finalHeight / 2
                break
            case 'tr':
                newCenterX = fixedX + finalWidth / 2
                newCenterY = fixedY - finalHeight / 2
                break
            case 'bl':
                newCenterX = fixedX - finalWidth / 2
                newCenterY = fixedY + finalHeight / 2
                break
            case 'br':
                newCenterX = fixedX + finalWidth / 2
                newCenterY = fixedY + finalHeight / 2
                break
            default:
                newCenterX = fixArea.left
                newCenterY = fixArea.top
        }

        // 应用新的位置和缩放
        fixArea.set({
            scaleX: finalScaleX,
            scaleY: finalScaleY,
            left: newCenterX,
            top: newCenterY
        })

        this.render._FC.renderAll()
    }



    private _handleEdgeScaling = (fixArea: Group, corner: string, targetBounds: any) => {
        const currentWidth = fixArea.width * fixArea.scaleX
        const currentHeight = fixArea.height * fixArea.scaleY
        const currentLeft = fixArea.left - currentWidth / 2
        const currentTop = fixArea.top - currentHeight / 2
        const currentRight = fixArea.left + currentWidth / 2
        const currentBottom = fixArea.top + currentHeight / 2

        let newScaleX = fixArea.scaleX
        let newScaleY = fixArea.scaleY
        let newLeft = fixArea.left
        let newTop = fixArea.top

        switch (corner) {
            case 'ml': { // 左边缘
                const maxWidthFromRight = currentRight - targetBounds.left
                const maxScaleXFromLeft = Math.min(maxWidthFromRight / fixArea.width, this.options.maxFixAreaWidth / fixArea.width)
                const minScaleXFromLeft = this.options.minFixAreaWidth / fixArea.width
                newScaleX = Math.max(minScaleXFromLeft, Math.min(maxScaleXFromLeft, fixArea.scaleX))
                newLeft = currentRight - (fixArea.width * newScaleX) / 2
                break
            }

            case 'mr': { // 右边缘
                const maxWidthFromLeft = targetBounds.right - currentLeft
                const maxScaleXFromRight = Math.min(maxWidthFromLeft / fixArea.width, this.options.maxFixAreaWidth / fixArea.width)
                const minScaleXFromRight = this.options.minFixAreaWidth / fixArea.width
                newScaleX = Math.max(minScaleXFromRight, Math.min(maxScaleXFromRight, fixArea.scaleX))
                newLeft = currentLeft + (fixArea.width * newScaleX) / 2
                break
            }

            case 'mt': { // 上边缘
                const maxHeightFromBottom = currentBottom - targetBounds.top
                const maxScaleYFromTop = Math.min(maxHeightFromBottom / fixArea.height, this.options.maxFixAreaHeight / fixArea.height)
                const minScaleYFromTop = this.options.minFixAreaHeight / fixArea.height
                newScaleY = Math.max(minScaleYFromTop, Math.min(maxScaleYFromTop, fixArea.scaleY))
                newTop = currentBottom - (fixArea.height * newScaleY) / 2
                break
            }

            case 'mb': { // 下边缘
                const maxHeightFromTop = targetBounds.bottom - currentTop
                const maxScaleYFromBottom = Math.min(maxHeightFromTop / fixArea.height, this.options.maxFixAreaHeight / fixArea.height)
                const minScaleYFromBottom = this.options.minFixAreaHeight / fixArea.height
                newScaleY = Math.max(minScaleYFromBottom, Math.min(maxScaleYFromBottom, fixArea.scaleY))
                newTop = currentTop + (fixArea.height * newScaleY) / 2
                break
            }
        }

        fixArea.set({
            scaleX: newScaleX,
            scaleY: newScaleY,
            left: newLeft,
            top: newTop
        })
    }

    /**
     * 导出包含修复区域的blob
     * @param options 导出选项
     * @param options.backgroundColor 背景色，默认为透明
     * @param options.fixAreaColor 修复区域填充色，默认为红色
     * @param options.format 导出格式，默认为'png'
     * @param options.quality 导出质量（仅对jpeg有效），默认为1
     * @returns Promise<Blob>
     */
    public exportBlob = async (options: {
        backgroundColor?: string;
        fixAreaColor?: string;
        format?: 'png' | 'jpeg' | 'webp';
        quality?: number;
        width?: number;
        height?: number;
    } = {}): Promise<Blob> => {
        const {
            backgroundColor = 'transparent',
            fixAreaColor = '#ff0000',
            format = 'png',
            quality = 1,
            width,
            height
        } = options;

        // 获取图片的原始尺寸和位置
        const targetBounds = getTargetBounds.call(this._fixTarget);
        const imageWidth = Math.ceil(width ?? targetBounds.right - targetBounds.left);
        const imageHeight = Math.ceil(height ?? targetBounds.bottom - targetBounds.top);
        // 创建离屏canvas
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = imageWidth;
        offscreenCanvas.height = imageHeight;
        const ctx = offscreenCanvas.getContext('2d')!;

        // 设置背景色
        if (backgroundColor !== 'transparent') {
            ctx.fillStyle = backgroundColor;
            ctx.fillRect(0, 0, imageWidth, imageHeight);
        }

        // 绘制修复区域
        ctx.fillStyle = fixAreaColor;

        for (const fixArea of this._fixAreas) {
            // 计算修复区域在图片坐标系中的位置和尺寸
            const actualWidth = fixArea.width * fixArea.scaleX;
            const actualHeight = fixArea.height * fixArea.scaleY;

            // 转换到图片坐标系（相对于图片左上角）
            const rectLeft = fixArea.left - actualWidth / 2 - targetBounds.left;
            const rectTop = fixArea.top - actualHeight / 2 - targetBounds.top;

            // 绘制矩形
            ctx.fillRect(rectLeft, rectTop, actualWidth, actualHeight);
        }

        // 转换为blob
        return new Promise((resolve, reject) => {
            offscreenCanvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Failed to create blob'));
                }
            }, `image/${format}`, quality);
        });
    }

    public get fixAreas() {
        return this._fixAreas
    }

    private removeAllFixArea = () => {
        this._fixAreas.forEach(item => {
            this.render._FC.remove(item)
        })
    }

    private _removeMask = () => {
        if (this.maskElement) {
            this.render._FC.remove(this.maskElement)
        }
    }

    public __destroy__ = () => {
        this._unBindEvent()
        this._removeMask()
        this.removeAllFixArea()
        this.render._FC.set('isDisabledContextMenu', false)
        this._render._FC.selection = true;
        this.render._unlockAll()
        if (this.options.renderMask) {
            if (this._viewportTranslateHandler) {
                this.render._FC.off('viewport:translate', this._viewportTranslateHandler);
            }
            if (this._viewportZoomHandler) {
                this.render._FC.off('viewport:zoom', this._viewportZoomHandler);
            }
        }
    }
}
