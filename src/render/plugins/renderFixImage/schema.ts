import { z } from "zod"

export const fixImageOptionsSchema = z.object({
    maxFixAreaWidth: z.number(),
    maxFixAreaHeight: z.number(),
    minFixAreaWidth: z.number(),
    minFixAreaHeight: z.number(),
    maxAreaCount: z.number(),
    fixAreaTemporaryColor: z.string().optional().default('#000000'),
    fixAreaTemporaryOpacity: z.number().optional().default(.8),
    renderMask: z.boolean().optional().default(false), // 是否开启渲染遮罩
    renderMaskColor: z.string().optional().default('#000000'), // 渲染遮罩颜色
    renderMaskOpacity: z.number().optional().default(0.5), // 渲染遮罩透明度
});