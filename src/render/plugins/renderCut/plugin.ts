import { z } from "zod"
import { Plugin } from '../../base/types';
import { Render } from '../../render';
import { cutOptionsSchema } from "./schema"
import { IImage } from '../../base/package/image/image';
import { renderMask, viewportTranslateHandler, viewportZoomHandler } from "../utils/mask";
import { BasicTransformEvent, Canvas, Group, Rect, Path, TPointerEvent } from "fabric";
import { getTargetBounds } from "../utils/fixRect";


type OptionsParams = z.infer<typeof cutOptionsSchema>
export class RenderCutPlugin extends Plugin {
    public __name__ = 'CutPlugin'
    public options: OptionsParams;
    private render: Render;
    private maskElement: Rect | undefined;

    private _viewportTranslateHandler;
    private _viewportZoomHandler;
    private _ImageTarget: IImage

    private targetParent: Group | Canvas
    private targetParentIndex: number

    _cutTarget:Group

    constructor(render: Render, options: OptionsParams & { target: IImage }) {
        super(render);

        const result = cutOptionsSchema.parse(options);
        this.options = result;
        this.render = render
        this.render._FC.set('isDisabledContextMenu', true)
        this.render._FC.discardActiveObject()
        this._ImageTarget = options.target
        this._cutTarget = new Group([], {
            width: this.options.maxCutAreaWidth,
            height: this.options.maxCutAreaHeight,
            backgroundColor: this.options.cutAreaTemporaryColor,
            opacity: this.options.cutAreaTemporaryOpacity,
            left: this._ImageTarget.left,
            top: this._ImageTarget.top
        })
        this._cutTarget.set('_name_', 'cutImageRect')
        this.targetParent = this._ImageTarget.parent || this.render._FC
        this.targetParentIndex = this.targetParent._objects.indexOf(this._ImageTarget)
        if (this.options.renderMask) {
            this.maskElement = renderMask.call(render, {
                maskColor: this.options.renderMaskColor,
                maskOpacity: this.options.renderMaskOpacity,
            });
            this._viewportTranslateHandler = viewportTranslateHandler.bind(this.maskElement, render);
            this._viewportZoomHandler = viewportZoomHandler.bind(this.maskElement, render);
            render._FC.on('viewport:translate', this._viewportTranslateHandler);
            render._FC.on('viewport:zoom', this._viewportZoomHandler);
        }

        // 从原有父级中移除
        this.targetParent._objects.splice(this.targetParentIndex, 1)
        // 添加到画布中最上级
        this.render._FC._objects.push(this._ImageTarget)

        // 确保初始化时的正确层级顺序
        this._ensureCorrectLayerOrder()

        this.render._FC.renderAll()
        this._bindEvent()
        this._render._FC.selection = false;
        this.render._lockAll()
        this.render._FC.add(this._cutTarget)
        this._cutTarget.selectable = true;
        this._cutTarget.evented = true;
        this._updateMaskHighlight()
    }

    private _bindEvent = () => {
        this.render._FC.on('object:added', this._ensureCorrectLayerOrder)
        this._cutTarget.on('scaling', this._onScaleCutArea)
        this._cutTarget.on('moving', this._onMovingCutTarget)
    }

    private _unBindEvent = () => {
        if (this.render._FC) {
            this.render._FC.off('object:added', this._ensureCorrectLayerOrder)
        }
        if (this._cutTarget) {
            this._cutTarget.off('scaling', this._onScaleCutArea)
            this._cutTarget.off('moving', this._onMovingCutTarget)
        }
    }


    private _ensureCorrectLayerOrder = () => {
        this.render._FC.bringObjectToFront(this._ImageTarget)
        if (this.options.renderMask && this.maskElement) {
            this.render._FC.bringObjectToFront(this.maskElement)
        }
        this.render._FC.bringObjectToFront(this._cutTarget)
    }

    private _updateMaskHighlight = () => {
        if (!this.maskElement || !this._cutTarget) return;

        // 获取cutTarget的实际尺寸和位置
        const actualWidth = this._cutTarget.width * this._cutTarget.scaleX;
        const actualHeight = this._cutTarget.height * this._cutTarget.scaleY;
        const cutLeft = this._cutTarget.left - actualWidth / 2;
        const cutTop = this._cutTarget.top - actualHeight / 2;

        // 创建高亮区域的剪切路径
        const highlightPath = `M${cutLeft},${cutTop} L${cutLeft + actualWidth},${cutTop} L${cutLeft + actualWidth},${cutTop + actualHeight} L${cutLeft},${cutTop + actualHeight} Z`;

        // 更新mask的clipPath属性来创建高亮效果
        this.maskElement.set({
            clipPath: new Path(highlightPath, {
                inverted: true,
                absolutePositioned: true
            })
        });

        this.render._FC.renderAll();
    }

    private _onMovingCutTarget = (e: BasicTransformEvent<TPointerEvent>) => {
        const cutArea = e.transform.target as Group
        const targetBounds = getTargetBounds.call(this._ImageTarget)

        // 计算缩放后的实际尺寸
        const actualWidth = cutArea.width * cutArea.scaleX
        const actualHeight = cutArea.height * cutArea.scaleY
        const halfWidth = actualWidth / 2
        const halfHeight = actualHeight / 2

        // 计算允许的移动范围
        const minX = targetBounds.left + halfWidth
        const maxX = targetBounds.right - halfWidth
        const minY = targetBounds.top + halfHeight
        const maxY = targetBounds.bottom - halfHeight

        // 限制在图片范围内移动
        let newX = cutArea.left
        let newY = cutArea.top

        if (minX <= maxX) {
            newX = Math.max(minX, Math.min(maxX, cutArea.left))
        } else {
            // 如果cutArea比图片还宽，居中显示
            newX = (targetBounds.left + targetBounds.right) / 2
        }

        if (minY <= maxY) {
            newY = Math.max(minY, Math.min(maxY, cutArea.top))
        } else {
            // 如果fixArea比图片还高，居中显示
            newY = (targetBounds.top + targetBounds.bottom) / 2
        }

        cutArea.set({
            left: newX,
            top: newY
        })
        this._updateMaskHighlight()
        this.render._FC.renderAll()
    }

    private _onScaleCutArea = (e: BasicTransformEvent<TPointerEvent>) => {
        const cutArea = e.transform.target as Group
        const corner = e.transform.corner
        const targetBounds = getTargetBounds.call(this._ImageTarget)

        // 计算当前cutArea的边界
        const currentWidth = cutArea.width * cutArea.scaleX
        const currentHeight = cutArea.height * cutArea.scaleY
        const currentLeft = cutArea.left - currentWidth / 2
        const currentTop = cutArea.top - currentHeight / 2
        const currentRight = cutArea.left + currentWidth / 2
        const currentBottom = cutArea.top + currentHeight / 2

        // 根据拖拽角落确定固定点和可变边界
        let fixedX: number, fixedY: number
        let maxNewWidth: number, maxNewHeight: number
        const minNewWidth = this.options.minCutAreaWidth
        const minNewHeight = this.options.minCutAreaHeight

        switch (corner) {
            case 'tl': // 左上角，固定右下角
                fixedX = currentRight
                fixedY = currentBottom
                maxNewWidth = Math.min(fixedX - targetBounds.left, this.options.maxCutAreaWidth)
                maxNewHeight = Math.min(fixedY - targetBounds.top, this.options.maxCutAreaHeight)
                break
            case 'tr': // 右上角，固定左下角
                fixedX = currentLeft
                fixedY = currentBottom
                maxNewWidth = Math.min(targetBounds.right - fixedX, this.options.maxCutAreaWidth)
                maxNewHeight = Math.min(fixedY - targetBounds.top, this.options.maxCutAreaHeight)
                break
            case 'bl': // 左下角，固定右上角
                fixedX = currentRight
                fixedY = currentTop
                maxNewWidth = Math.min(fixedX - targetBounds.left, this.options.maxCutAreaWidth)
                maxNewHeight = Math.min(targetBounds.bottom - fixedY, this.options.maxCutAreaHeight)
                break
            case 'br': // 右下角，固定左上角
                fixedX = currentLeft
                fixedY = currentTop
                maxNewWidth = Math.min(targetBounds.right - fixedX, this.options.maxCutAreaWidth)
                maxNewHeight = Math.min(targetBounds.bottom - fixedY, this.options.maxCutAreaHeight)
                break
            default:
                // 边缘拖拽，处理单方向缩放
                this._handleEdgeScaling(cutArea, corner, targetBounds)
                return
        }

        // 使用当前实际尺寸计算宽高比（保持当前状态的比例）
        const currentAspectRatio = currentWidth / currentHeight

        // 计算新的尺寸（基于当前缩放状态）
        const newWidth = cutArea.width * cutArea.scaleX
        const newHeight = cutArea.height * cutArea.scaleY

        // 对角缩放时，保持当前fixArea的实际宽高比
        // 计算缩放因子（取宽度和高度缩放的平均值，保持比例）
        const scaleFactorX = newWidth / currentWidth
        const scaleFactorY = newHeight / currentHeight
        const averageScaleFactor = (scaleFactorX + scaleFactorY) / 2

        // 应用平均缩放因子，保持当前比例
        let finalWidth = currentWidth * averageScaleFactor
        let finalHeight = currentHeight * averageScaleFactor

        // 应用边界限制
        finalWidth = Math.max(minNewWidth, Math.min(maxNewWidth, finalWidth))
        finalHeight = Math.max(minNewHeight, Math.min(maxNewHeight, finalHeight))

        // 如果边界限制破坏了比例，重新调整
        const constrainedAspectRatio = finalWidth / finalHeight
        if (Math.abs(constrainedAspectRatio - currentAspectRatio) > 0.01) {
            // 选择限制更严格的方向作为主导
            const widthLimitRatio = finalWidth / currentWidth
            const heightLimitRatio = finalHeight / currentHeight

            if (widthLimitRatio < heightLimitRatio) {
                // 宽度限制更严格，以宽度为准
                finalHeight = finalWidth / currentAspectRatio
            } else {
                // 高度限制更严格，以高度为准
                finalWidth = finalHeight * currentAspectRatio
            }
        }

        // 计算新的缩放比例
        const newScaleX = finalWidth / cutArea.width
        const newScaleY = finalHeight / cutArea.height

        // 确保角落缩放时保持等比例
        const finalScaleX = newScaleX
        const finalScaleY = newScaleY

        // 计算新的中心点位置（保持固定点不变）
        let newCenterX: number, newCenterY: number

        switch (corner) {
            case 'tl':
                newCenterX = fixedX - finalWidth / 2
                newCenterY = fixedY - finalHeight / 2
                break
            case 'tr':
                newCenterX = fixedX + finalWidth / 2
                newCenterY = fixedY - finalHeight / 2
                break
            case 'bl':
                newCenterX = fixedX - finalWidth / 2
                newCenterY = fixedY + finalHeight / 2
                break
            case 'br':
                newCenterX = fixedX + finalWidth / 2
                newCenterY = fixedY + finalHeight / 2
                break
            default:
                newCenterX = cutArea.left
                newCenterY = cutArea.top
        }

        // 应用新的位置和缩放
        cutArea.set({
            scaleX: finalScaleX,
            scaleY: finalScaleY,
            left: newCenterX,
            top: newCenterY
        })

        // 更新mask高亮区域
        this._updateMaskHighlight();
        this.render._FC.renderAll()
    }



    private _handleEdgeScaling = (fixArea: Group, corner: string, targetBounds: any) => {
        const currentWidth = fixArea.width * fixArea.scaleX
        const currentHeight = fixArea.height * fixArea.scaleY
        const currentLeft = fixArea.left - currentWidth / 2
        const currentTop = fixArea.top - currentHeight / 2
        const currentRight = fixArea.left + currentWidth / 2
        const currentBottom = fixArea.top + currentHeight / 2

        let newScaleX = fixArea.scaleX
        let newScaleY = fixArea.scaleY
        let newLeft = fixArea.left
        let newTop = fixArea.top

        switch (corner) {
            case 'ml': { // 左边缘
                const maxWidthFromRight = currentRight - targetBounds.left
                const maxScaleXFromLeft = Math.min(maxWidthFromRight / fixArea.width, this.options.maxCutAreaWidth / fixArea.width)
                const minScaleXFromLeft = this.options.minCutAreaWidth / fixArea.width
                newScaleX = Math.max(minScaleXFromLeft, Math.min(maxScaleXFromLeft, fixArea.scaleX))
                newLeft = currentRight - (fixArea.width * newScaleX) / 2
                break
            }

            case 'mr': { // 右边缘
                const maxWidthFromLeft = targetBounds.right - currentLeft
                const maxScaleXFromRight = Math.min(maxWidthFromLeft / fixArea.width, this.options.maxCutAreaWidth / fixArea.width)
                const minScaleXFromRight = this.options.minCutAreaWidth / fixArea.width
                newScaleX = Math.max(minScaleXFromRight, Math.min(maxScaleXFromRight, fixArea.scaleX))
                newLeft = currentLeft + (fixArea.width * newScaleX) / 2
                break
            }

            case 'mt': { // 上边缘
                const maxHeightFromBottom = currentBottom - targetBounds.top
                const maxScaleYFromTop = Math.min(maxHeightFromBottom / fixArea.height, this.options.maxCutAreaHeight / fixArea.height)
                const minScaleYFromTop = this.options.minCutAreaHeight / fixArea.height
                newScaleY = Math.max(minScaleYFromTop, Math.min(maxScaleYFromTop, fixArea.scaleY))
                newTop = currentBottom - (fixArea.height * newScaleY) / 2
                break
            }

            case 'mb': { // 下边缘
                const maxHeightFromTop = targetBounds.bottom - currentTop
                const maxScaleYFromBottom = Math.min(maxHeightFromTop / fixArea.height, this.options.maxCutAreaHeight / fixArea.height)
                const minScaleYFromBottom = this.options.minCutAreaHeight / fixArea.height
                newScaleY = Math.max(minScaleYFromBottom, Math.min(maxScaleYFromBottom, fixArea.scaleY))
                newTop = currentTop + (fixArea.height * newScaleY) / 2
                break
            }
        }

        fixArea.set({
            scaleX: newScaleX,
            scaleY: newScaleY,
            left: newLeft,
            top: newTop
        })
        this._updateMaskHighlight()
    }

    /**
     * 导出包含修复区域的blob
     * @param options 导出选项
     * @param options.backgroundColor 背景色，默认为透明
     * @param options.fixAreaColor 修复区域填充色，默认为红色
     * @param options.format 导出格式，默认为'png'
     * @param options.quality 导出质量（仅对jpeg有效），默认为1
     * @returns Promise<Blob>
     */
    public exportBlob = async (options: {
        backgroundColor?: string;
        fixAreaColor?: string;
        format?: 'png' | 'jpeg' | 'webp';
        quality?: number;
        width?: number;
        height?: number;
    } = {}): Promise<Blob> => {
        const {
            backgroundColor = 'transparent',
            fixAreaColor = '#ff0000',
            format = 'png',
            quality = 1,
            width,
            height
        } = options;

        // 获取图片的原始尺寸和位置
        const targetBounds = getTargetBounds.call(this._ImageTarget);
        const imageWidth = Math.ceil(width ?? targetBounds.right - targetBounds.left);
        const imageHeight = Math.ceil(height ?? targetBounds.bottom - targetBounds.top);
        // 创建离屏canvas
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = imageWidth;
        offscreenCanvas.height = imageHeight;
        const ctx = offscreenCanvas.getContext('2d')!;

        // 设置背景色
        if (backgroundColor !== 'transparent') {
            ctx.fillStyle = backgroundColor;
            ctx.fillRect(0, 0, imageWidth, imageHeight);
        }

        // 绘制修复区域
        ctx.fillStyle = fixAreaColor;
        const fixArea = this._cutTarget
            // 计算修复区域在图片坐标系中的位置和尺寸
            const actualWidth = fixArea.width * fixArea.scaleX;
            const actualHeight = fixArea.height * fixArea.scaleY;

            // 转换到图片坐标系（相对于图片左上角）
            const rectLeft = fixArea.left - actualWidth / 2 - targetBounds.left;
            const rectTop = fixArea.top - actualHeight / 2 - targetBounds.top;

            // 绘制矩形
            ctx.fillRect(rectLeft, rectTop, actualWidth, actualHeight);

        // 转换为blob
        return new Promise((resolve, reject) => {
            offscreenCanvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Failed to create blob'));
                }
            }, `image/${format}`, quality);
        });
    }

    public get fixAreas() {
        return this._cutTarget
    }

    /**
     * 获取选择框的边界信息
     * @returns 边界信息对象，包含位置、尺寸和中心点
     */
    public getSelectionBounds = () => {
        const actualWidth = this._cutTarget.width * this._cutTarget.scaleX;
        const actualHeight = this._cutTarget.height * this._cutTarget.scaleY;
        const left = this._cutTarget.left - actualWidth / 2;
        const top = this._cutTarget.top - actualHeight / 2;

        return {
            left,
            top,
            width: actualWidth,
            height: actualHeight,
            centerX: this._cutTarget.left,
            centerY: this._cutTarget.top,
        };
    }

    /**
     * 设置选择框的位置和大小
     * @param options 选择框配置选项
     */
    public setSelectionBox = (options: {
        left?: number;
        top?: number;
        width?: number;
        height?: number;
    }) => {
        const { left, top, width, height } = options;

        // 获取目标图片的边界
        const targetBounds = getTargetBounds.call(this._ImageTarget);

        // 计算新的尺寸（应用边界限制）
        let newWidth = width ?? this._cutTarget.width * this._cutTarget.scaleX;
        let newHeight = height ?? this._cutTarget.height * this._cutTarget.scaleY;

        // 应用最小和最大尺寸限制
        newWidth = Math.max(this.options.minCutAreaWidth, Math.min(this.options.maxCutAreaWidth, newWidth));
        newHeight = Math.max(this.options.minCutAreaHeight, Math.min(this.options.maxCutAreaHeight, newHeight));

        // 确保不超出图片边界
        newWidth = Math.min(newWidth, targetBounds.right - targetBounds.left);
        newHeight = Math.min(newHeight, targetBounds.bottom - targetBounds.top);

        // 计算新的缩放比例
        const newScaleX = newWidth / this._cutTarget.width;
        const newScaleY = newHeight / this._cutTarget.height;

        // 计算新的位置（如果提供了位置参数）
        let newLeft = left ?? this._cutTarget.left;
        let newTop = top ?? this._cutTarget.top;

        // 确保选择框不超出图片边界
        const halfWidth = newWidth / 2;
        const halfHeight = newHeight / 2;

        newLeft = Math.max(targetBounds.left + halfWidth, Math.min(targetBounds.right - halfWidth, newLeft));
        newTop = Math.max(targetBounds.top + halfHeight, Math.min(targetBounds.bottom - halfHeight, newTop));

        // 应用新的属性
        this._cutTarget.set({
            scaleX: newScaleX,
            scaleY: newScaleY,
            left: newLeft,
            top: newTop,
        });

        // 更新遮罩高亮
        this._updateMaskHighlight();
        this._cutTarget.setCoords()
        this.render._FC.renderAll();
    }

    /**
     * 截图方法 - 导出选择区域的图片
     * @param options 导出选项
     * @returns Promise<Blob> 截图的Blob对象
     */
    public captureScreenshot = async (options: {
        exportType?: 'png' | 'jpeg' | 'webp';
        quality?: number;
        multiplier?: number;
    } = {}): Promise<Blob> => {
        const { exportType = 'png', quality = 1, multiplier = 1 } = options;

        // 获取选择区域的边界
        const bounds = this.getSelectionBounds();
        const targetBounds = getTargetBounds.call(this._ImageTarget);

        // 计算选择区域在图片中的相对位置和尺寸
        const cropLeft = bounds.left - targetBounds.left;
        const cropTop = bounds.top - targetBounds.top;
        const cropWidth = bounds.width;
        const cropHeight = bounds.height;

        // 直接使用图片对象的toCanvasElement方法生成canvas
        const imageCanvas = this._ImageTarget.toCanvasElement({
            multiplier: multiplier,
            left: cropLeft,
            top: cropTop,
            width: cropWidth,
            height: cropHeight,
        });

        // 转换为Blob
        return new Promise((resolve, reject) => {
            imageCanvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Failed to create screenshot blob'));
                }
            }, `image/${exportType}`, quality);
        });
    }

    private removeAllFixArea = () => {
        if (this._cutTarget && this.render._FC._objects.includes(this._cutTarget)) {
            this.render._FC.remove(this._cutTarget)
        }
    }

    private _removeMask = () => {
        if (this.maskElement && this.render._FC._objects.includes(this.maskElement)) {
            this.render._FC.remove(this.maskElement)
            this.maskElement = undefined
        }
    }

    public __destroy__ = () => {
        // 1. 先解绑事件
        this._unBindEvent()
        // 2. 恢复画布状态
        this.render._FC.discardActiveObject()
        this.render._FC.set('isDisabledContextMenu', false)
        this._render._FC.selection = true;
        this.render._unlockAll()
        // 3. 移除事件监听器
        if (this.options.renderMask) {
            if (this._viewportTranslateHandler) {
                this.render._FC.off('viewport:translate', this._viewportTranslateHandler);
            }
            if (this._viewportZoomHandler) {
                this.render._FC.off('viewport:zoom', this._viewportZoomHandler);
            }
        }

        // 4. 恢复图片到原来的位置（在移除其他对象之前）
        if (this._ImageTarget && this.targetParent && this.targetParentIndex >= 0) {
            // 从画布顶层移除
            const currentIndex = this.render._FC._objects.indexOf(this._ImageTarget);
            if (currentIndex >= 0) {
                this.render._FC._objects.splice(currentIndex, 1);
            }

            // 恢复到原来的父级和位置
            if (this.targetParent._objects) {
                this.targetParent._objects.splice(this.targetParentIndex, 0, this._ImageTarget);
            }
        }

        // 5. 移除修复区域和遮罩
        this.removeAllFixArea()
        this._removeMask()

        // 6. 清理对象引用
        const parent = this._cutTarget?.parent ?? this.render._FC
        parent.remove(this._cutTarget)
        this.maskElement = undefined
        this._ImageTarget = null as any
        this.targetParent = null as any

        // 7. 最后渲染
        this.render._FC.renderAll();
    }
}
