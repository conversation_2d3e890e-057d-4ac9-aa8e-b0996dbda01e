import { z } from "zod"

export const cutOptionsSchema = z.object({
    maxCutAreaWidth: z.number().optional().default(Infinity),
    maxCutAreaHeight: z.number().optional().default(Infinity),
    minCutAreaWidth: z.number(),
    minCutAreaHeight: z.number(),
    cutAreaTemporaryColor: z.string().optional().default('#000000'),
    cutAreaTemporaryOpacity: z.number().optional().default(.8),
    renderMask: z.boolean().optional().default(false), // 是否开启渲染遮罩
    renderMaskColor: z.string().optional().default('#000000'), // 渲染遮罩颜色
    renderMaskOpacity: z.number().optional().default(0.5), // 渲染遮罩透明度
});