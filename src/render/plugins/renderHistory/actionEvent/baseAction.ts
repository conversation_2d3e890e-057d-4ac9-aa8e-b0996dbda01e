import {
  ActiveSelection,
  CanvasEvents,
  FabricObject,
  TPointerEvent,
  TPointerEventInfo,
} from 'fabric';
import { Render } from '../../../../render/render';
import { Operation } from '../operation';
import { getElementOptions } from '../../../../render/utils/shapes';
import { DeltaType } from '../types';
import { History } from '../history';
import { ElementOptions } from '../../../../render/utils/types';
import isEqual from 'lodash-es/isEqual';
export class BaseAction {
  private _render: Render;
  private history: History;
  private preElementOptions: ElementOptions[] | [] = [];
  constructor(render: Render, history: History, private isDisabledDefault: boolean, private isDisabledMask: boolean) {
    this._render = render;
    this.history = history;
    this.bindEvents();
  }

  private bindEvents(): void {
    this.handleEvent(true);
  }

  private handleEvent = (eventSwitch: boolean) => {
    const events = {
      /**
       * 通过鼠标mouse:down事件记录 更改前的元素
       */
      'mouse:down': [this.handleBeforeModify],

      /**
       * 通过鼠标mouse:up事件记录元素 transform 操作 （ 移动、缩放、旋转）后
       */
      'mouse:up': [this.handleModified],
      // 笔刷历史监听
      'mask:path:before': [this.handleMaskBeforeCreated],
      'mask:rect:before': [this.handleMaskBeforeCreated],
      'mask:lasso:before': [this.handleMaskBeforeCreated],
      'mask:smart:before': [this.handleMaskBeforeCreated],
      'mask:path:created': [this.handleMaskCreated],
      'mask:rect:created': [this.handleMaskCreated],
      'mask:lasso:created': [this.handleMaskCreated],
      'mask:smart:created': [this.handleMaskCreated],

    };

    Object.entries(events).forEach(([event, handlers]) =>
      handlers.forEach((handler) =>
        eventSwitch
          ? this._render._FC.on(event as keyof CanvasEvents, handler)
          : this._render._FC.off(event as keyof CanvasEvents, handler)
      )
    );
  };

  private handleBeforeModify = (e: { target: FabricObject }) => {
    if (this.isDisabledDefault) return;
    const operation = this.getElementData(e);
    this.preElementOptions = operation || [];
  };

  private handleModified = (e: { target: FabricObject }) => {
    if (this.isDisabledDefault) return;
    const elementOptions = this.getElementData(e);
    const operation = this.getModifiedOperation({
      beforeData: this.preElementOptions,
      afterData: elementOptions,
    });
    this.preElementOptions = [];
    if (!operation) return;
    this.history.submit(operation);
    this.history.onInteractionModifiedCallback?.(operation);
  };

  public getElementData = (
    e: TPointerEventInfo<TPointerEvent> | { target: FabricObject }
  ) => {
    if (!e.target) return [];
    if (e.target instanceof ActiveSelection) {
      return this._render.setOptionsZIndex(
        e.target.getObjects().map((object) => getElementOptions.call(this._render, object))
      );
    }
    const realElement = this._render.Finder.findRealElement(e.target)
    if (realElement) {
      return this._render.setOptionsZIndex([getElementOptions.call(this._render, realElement)])
    }
    return []
  };

  public getModifiedOperation = ({
    beforeData,
    afterData,
    selectable = true,
  }: {
    beforeData: ElementOptions[];
    afterData: ElementOptions[];
    selectable?: boolean;
  }) => {
    //不校验_custom_data属性
    const checkPreElementOptions = beforeData.map((option) => {
      const { _custom_data_, ...restOption } = option;
      return restOption;
    });
    const checkElementOptions = afterData.map((option) => {
      const { _custom_data_, ...restOption } = option;
      return restOption;
    });
    if (
      beforeData.length != afterData.length ||
      isEqual(checkPreElementOptions, checkElementOptions)
    ) {
      return;
    }

    const operation = new Operation(
      {
        type: DeltaType.MODIFY,
        selectable,
        preData: {
          objects: beforeData,
        },
        afterData: {
          objects: afterData,
        },
        undoHandler: undefined,
        redoHandler: undefined,
      },
      this._render
    );
    return operation;
  };

  public handleMaskBeforeCreated = (e: { target: FabricObject, element: FabricObject }) => {
    if (this.isDisabledMask) return;
    const operation = this.getElementData({ target: e.element });
    this.preElementOptions = operation || [];
  }

  public handleMaskCreated = (e: { target: FabricObject, element: FabricObject }) => {
    if (this.isDisabledMask) return;
    const elementOptions = this.getElementData({ target: e.element });
    const operation = this.getModifiedOperation({
      beforeData: this.preElementOptions,
      afterData: elementOptions,
    });
    this.preElementOptions = [];
    if (!operation) return;
    this.history.submit(operation);
    this.history.onInteractionModifiedCallback?.(operation);
  }



  public getAddOperation = ({
    objects,
    selectable = true,
  }: {
    objects: FabricObject[];
    selectable?: boolean;
  }) => {
    if (!objects) return;

    const targetData = this._render.setOptionsZIndex(
      objects.map((object) => {
        return getElementOptions.call(this._render, object);
      })
    );
    const operation = new Operation(
      {
        type: DeltaType.INSERT,
        selectable,
        preData: null,
        afterData: {
          objects: targetData,
        },
        undoHandler: undefined,
        redoHandler: undefined,
      },
      this._render
    );
    return operation;
  };

  public getRemoveOperation = ({
    objects,
    selectable = true,
  }: {
    objects: FabricObject[];
    selectable?: boolean;
  }) => {
    if (!objects) return;
    const targetData = this._render.setOptionsZIndex(
      objects.map((object) => getElementOptions.call(this._render, object))
    );
    const operation = new Operation(
      {
        type: DeltaType.DELETE,
        selectable,
        preData: {
          objects: targetData,
        },
        afterData: null,
        undoHandler: undefined,
        redoHandler: undefined,
      },

      this._render
    );
    return operation;
  };

  public getZIndexOperation = (
    preData: ElementOptions[],
    afterData: ElementOptions[],
    type:
      | DeltaType.FRONT
      | DeltaType.BACK
      | DeltaType.FORWARD
      | DeltaType.BACKWARD
      | DeltaType.CHANGE_Z_INDEX
  ) => {
    const operation = new Operation(
      {
        type: type,
        selectable: true,
        preData: {
          objects: preData,
        },
        afterData: {
          objects: afterData,
        },
        undoHandler: undefined,
        redoHandler: undefined,
      },
      this._render
    );
    return operation;
  };

  public getLoadOperation = (objects: FabricObject[], selectable?: boolean) => {
    if (!objects) return;
    const targetData = objects.map((object) => getElementOptions.call(this._render, object));
    const operation = new Operation(
      {
        type: DeltaType.LOADING,
        selectable: selectable ?? true,
        preData: {
          objects: targetData,
        },
        afterData: null,
        undoHandler: undefined,
        redoHandler: undefined,
      },

      this._render
    );
    return operation;
  };

  public setIsDisabledDefault = (isDisabledDefault: boolean) => {
    this.isDisabledDefault = isDisabledDefault;
  };

  public destroy = () => {
    this.handleEvent(false);
  };
}
