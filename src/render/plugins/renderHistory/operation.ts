import { ElementOptions, ImageCustomOptions, ImageOptions, TextElementOptions } from '../../utils/types';
import { Render } from '../../../render/render';
import { Delta, DeltaType } from './types';
import { ActiveSelection, Canvas, FabricObject, Group } from 'fabric';
import { createElement, ElementName, replaceImage, WarningType } from '../../utils';
import { Frame } from '../../base/package/frame/frame';
import { Container } from '../../base/package/container/container';
import { rejectMerge, rejectSplit, resolveMerge, resolveSplit } from './utils/merge';
import { transformMatrix } from '../../utils/transformMatrix';

export class Operation<Type extends DeltaType = DeltaType> {
  constructor(public options: Delta<Type>, public render: Render) { }

  public async undo() {
    const { type } = this.options;
    switch (type) {
      case DeltaType.MERGE: {
        if (this.options.preData) {
          await rejectMerge.call(this.render, this);
        }
        return;
      }
      case DeltaType.SPLIT: {
        if (this.options.preData) {
          await rejectSplit.call(this.render, this);
        }
        return;
      }
      case DeltaType.INSERT: {
        if (this.options.afterData) {
          this.removeById(this.options.afterData);
        }
        return;
      }
      case DeltaType.DELETE: {
        if (this.options.preData) {
          await this.addTarget(this.options.preData);
        }
        return;
      }
      case DeltaType.MODIFY: {
        if (this.options.preData) {
          await this.modifyTarget(this.options.preData);
        }
        return;
      }

      case DeltaType.FRONT: {
        if (this.options.preData) {
          await this.undoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      case DeltaType.BACK: {
        if (this.options.preData) {
          await this.undoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      case DeltaType.FORWARD: {
        if (this.options.preData) {
          await this.undoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      case DeltaType.BACKWARD: {
        if (this.options.preData) {
          await this.undoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      case DeltaType.CHANGE_Z_INDEX: {
        if (this.options.preData) {
          await this.undoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      case DeltaType.LOADING: {
        if (this.options.preData) {
          await this.handleLoadObject(
            this.options.preData as { objects: ElementOptions[] }
          );
        }
        return;
      }
      // case DeltaType.LOADED: {
      //   if (this.options.preData) {
      //     await this.handleLoadObject(
      //       this.options.preData as { objects: ElementOptions[] },
      //       HistoryChangeType.UNDO
      //     );
      //   }
      //   return;
      // }
      case DeltaType.Handler: {
        if (this.options.undoHandler) {
          this.options.undoHandler();
        }
        return;
      }
    }
  }

  public async redo() {
    const { type } = this.options;
    switch (type) {
      case DeltaType.MERGE: {
        if (this.options.afterData) {
          await resolveMerge.call(this.render, this);
        }
        return;
      }
      case DeltaType.SPLIT: {
        if (this.options.afterData) {
          await resolveSplit.call(this.render, this);
        }
        return
      }
      case DeltaType.INSERT: {
        if (this.options.afterData) {
          await this.addTarget(this.options.afterData);
        }
        return;
      }
      case DeltaType.DELETE: {
        if (this.options.preData) {
          this.removeById(this.options.preData);
        }
        return;
      }
      case DeltaType.MODIFY: {
        if (this.options.afterData) {
          await this.modifyTarget(this.options.afterData);
        }
        return;
      }

      case DeltaType.FRONT: {
        if (this.options.preData) {
          await this.redoObjectZIndex(
            this.options.afterData as { objects: ElementOptions[] },
            DeltaType.FRONT
          );
        }
        return;
      }
      case DeltaType.BACK: {
        if (this.options.preData) {
          await this.redoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] },
            DeltaType.BACK
          );
        }
        return;
      }
      case DeltaType.FORWARD: {
        if (this.options.preData) {
          await this.redoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] },
            DeltaType.FORWARD
          );
        }
        return;
      }
      case DeltaType.BACKWARD: {
        if (this.options.preData) {
          await this.redoObjectZIndex(
            this.options.preData as { objects: ElementOptions[] },
            DeltaType.BACKWARD
          );
        }
        return;
      }
      case DeltaType.CHANGE_Z_INDEX: {
        if (this.options.afterData) {
          await this.redoObjectZIndex(
            this.options.afterData as { objects: ElementOptions[] },
            DeltaType.BACKWARD
          );
        }
        return;
      }
      // case DeltaType.LOADING: {
      //   if (this.options.preData) {
      //     await this.handleLoadObject(
      //       this.options.preData as { objects: ElementOptions[] },
      //       HistoryChangeType.REDO
      //     );
      //   }
      //   return;
      // }
      // case DeltaType.LOADED: {
      //   if (this.options.preData) {
      //     await this.handleLoadObject(
      //       this.options.preData as { objects: ElementOptions[] },
      //       HistoryChangeType.REDO
      //     );
      //   }
      //   return;
      // }
      case DeltaType.Handler: {
        if (this.options.redoHandler) {
          this.options.redoHandler();
        }
        return;
      }
    }
  }

  private removeById = ({ objects }: { objects: ElementOptions[] }) => {
    if (!objects.length) return;
    for (const object of objects) {
      const instance = this.render.Finder.findById(object._id_);
      const parent = this.render.Finder.findById(object._parent_id_);
      if (instance instanceof Canvas) return;
      if (instance) (parent as Group).remove(instance);
    }
    this.render._FC.requestRenderAll();
  };

  private addTarget = async ({ objects }: { objects: ElementOptions[] }) => {
    for (const object of objects) {
      const instance = await createElement(object);
      const parent = this.render.Finder.findById(object._parent_id_);
      (parent as Group).insertAt(object?.zIndex ?? 0, instance);
    }
    this.render._FC.requestRenderAll();
  };

  private modifyTarget = async ({ objects }: { objects: ElementOptions[] }) => {
    this.render._FC._discardActiveObject();
    const images = objects.filter(item => item._name_ === ElementName.IMAGE)
    const others = objects.filter(item => item._name_ !== ElementName.IMAGE)

    const els: FabricObject[] = []
    // 处理非图片对象
    for (const object of others) {
      const target = this.render.Finder.findById(object._id_);
      if (target instanceof Canvas) continue;
      els.push(target)
      const targetParent = this.render.Finder.findById(object._parent_id_) as Frame | Container | Canvas ?? this.render._FC
      const currentParent = target.parent as Frame | Container | Canvas ?? this.render._FC
      const realParent = targetParent ?? this.render._FC
      targetParent.set("dirty", true)
      currentParent.remove(target);
      realParent.insertAt(object?.zIndex ?? 0, target)
      target.set({
        ...object,
        charSpacing: (object as TextElementOptions).letterSpacing,
        left: targetParent instanceof Canvas ? object.left : object.relativelyLeft,
        top: targetParent instanceof Canvas ? object.top : object.relativelyTop,
        _parent_id_: targetParent instanceof Canvas ? '' : targetParent?._id_ ?? '',
        scaleY: object.scaleY / (realParent instanceof Canvas ? 1 : transformMatrix(realParent).scaleY),
        scaleX: object.scaleX / (realParent instanceof Canvas ? 1 : transformMatrix(realParent).scaleX),
      });

      this.render._FC.fire('object:changed', { target });
    }
    // 处理图片对象
    for (const object of images) {
      const target = this.render.Finder.findById(object._id_)
      if (target instanceof Canvas) continue;
      els.push(target)
      await replaceImage.call(this.render, target as Group, {
        src: (object as ImageOptions).src,
        ...object as ImageCustomOptions,
      });
      const targetParent = this.render.Finder.findById(object._parent_id_) as Frame | Container | Canvas;
      const currentParent = target.parent as Frame | Container | Canvas ?? this.render._FC
      const realParent = targetParent ?? this.render._FC
      currentParent.remove(target);
      realParent.insertAt(object?.zIndex ?? 0, target)
      target.set({
        _parent_id_: targetParent instanceof Canvas ? '' : targetParent?._id_ ?? '',
        left: targetParent instanceof Canvas ? object.left : object.relativelyLeft,
        top: targetParent instanceof Canvas ? object.top : object.relativelyTop,
        scaleY: object.scaleY / (realParent instanceof Canvas ? 1 : transformMatrix(realParent).scaleY),
        scaleX: object.scaleX / (realParent instanceof Canvas ? 1 : transformMatrix(realParent).scaleX),
      });
      this.render._FC.fire('object:changed', { target });
    }
    if (els.length === 0) return;
    if (!this.render._FC.selection) return
    if (els.length > 1) {
      const AO = new ActiveSelection(els, {
        canvas: this.render._FC,
      });
      this.render._FC.setActiveObject(AO);
    } else {
      this.render._FC.setActiveObject(els[0]);
    }
    this.render._FC.renderAll();
    if (els.length > 1) {
      this.render._Railings.drawRailingHandler();
    }
  };

  private undoObjectZIndex = ({ objects }: { objects: ElementOptions[] }) => {
    if (!objects.length) return;
    if (objects.length > 1) {
      this.render._FC.discardActiveObject();
    }
    objects.forEach((object) => {
      const instance = this.render.Finder.findById(object._id_);
      let parent: Canvas | Group = this.render._FC;
      if (instance instanceof Canvas) return;
      if (instance?.group) {
        parent = instance?.group;
      }
      if (instance) {
        parent.remove(instance);
        parent.insertAt(object?.zIndex ?? 0, instance);
      }
      return instance as FabricObject;
    });
    this.render._FC.requestRenderAll();
  };

  private redoObjectZIndex = (
    { objects }: { objects: ElementOptions[] },
    type:
      | DeltaType.FRONT
      | DeltaType.BACK
      | DeltaType.FORWARD
      | DeltaType.BACKWARD
      | DeltaType.CHANGE_Z_INDEX
  ) => {
    if (!objects.length) return;
    const instances = objects.map(
      (object) => this.render.Finder.findById(object._id_) as FabricObject
    );
    if (type === DeltaType.FRONT) {
      this.render.Actions.bringToFront(instances);
    } else if (type === DeltaType.BACK) {
      this.render.Actions.sendToBack(instances);
    } else if (type === DeltaType.FORWARD) {
      this.render.Actions.bringForward(instances);
    } else if (type === DeltaType.BACKWARD) {
      this.render.Actions.sendBackward(instances);
    } else if (type === DeltaType.CHANGE_Z_INDEX) {
      instances.forEach((instance, index) => {
        this.render.Actions.sendZIndex(instance, objects[index].zIndex ?? 0)
      })
    }
    this.render._FC.requestRenderAll();
  };

  private handleLoadObject = ({ objects }: { objects: ElementOptions[] }) => {
    objects.map((object) => {
      const instance = this.render.Finder.findById(object._id_);
      this.render.logger.info(`loading-before-options:`, object);
      this.render.logger.info(`current-instance:`, instance);
      if (instance instanceof Canvas) return;
      if (instance?.get('_loading_')) {
        this.render?.Actions.setLoaded(object._id_);
        this.render._FC.fire('warning', {
          type: WarningType.TASK_CANCEL,
          target: instance,
        });
      }
      // for (const key in object) {
      //   if (key === '_loading_') continue;
      //   instance?.set(
      //     key as keyof ElementOptions,
      //     object[key as keyof ElementOptions]
      //   );
      // }
      this.render._FC.fire('object:changed', { target: instance });
      this.render.Actions.setCancelError(object._id_);
    });
  };
}
