import { getVisibleObjects } from "../../utils/canvas";
import { Canvas, CanvasEvents, FabricObject, Group, Point, TCornerPoint } from "fabric";
import { OptionsSchema } from "./schema";
import { z } from "zod/v4";
import { cornerMapping, getPositions, getSnapScaleFromNearestLines } from "./utils";
import { isRealElement } from "../../utils/shapes";
import { Finder } from "../../base/finder";

type VerticalLineCoords = {
  x: number;
  y1: number;
  y2: number;
};

type HorizontalLineCoords = {
  y: number;
  x1: number;
  x2: number;
};

type ACoordsAppendCenter = NonNullable<FabricObject["aCoords"]> & {
  c: Point;
};

const getKeys = <T extends object>(obj: T): (keyof T)[] => {
  return Object.keys(obj) as (keyof T)[];
};

export class GuideLine {
  private canvasEvents: any;
  private isScale: boolean = false;

  private aligningLineMargin = 10; // 对齐线的吸附阀值
  private aligningLineWidth = 1;
  private aligningLineColor = "#F68066";

  private verticalLines: VerticalLineCoords[] = [];
  private horizontalLines: HorizontalLineCoords[] = [];

  private activeObj: FabricObject | undefined;

  private dirty = false; // 是否需要重绘

  private corner: string | undefined;

  constructor(private canvas: Canvas, private options?: Partial<z.infer<typeof OptionsSchema>>) {
    if (!(canvas instanceof Canvas)) {
      throw new Error("The provided canvas is not an instance of Canvas.");
    }

    const optionsParsed = OptionsSchema.parse(this.options ?? {});
    this.aligningLineMargin = optionsParsed.aligningLineMargin;
    this.aligningLineWidth = optionsParsed.aligningLineWidth;
    this.aligningLineColor = optionsParsed.aligningLineColor;

    this.canvasEvents = {
      "before:render": this.clearGuideline.bind(this),
      "after:render": this.drawGuideLines.bind(this),
      "object:moving": this.objectMoving.bind(this),
      "mouse:up": this.handleMouseUp.bind(this),
    };
    if (options?.enableScaleAdsorption) {
      this.canvasEvents["object:scaling"] = this.objectScaling.bind(this)
    }
    canvas.on(this.canvasEvents);
  }

  private objectScaling = (e: CanvasEvents["object:scaling"]) => {
    if (!isRealElement(e.target)) return;
    this.isScale = true;
    this.clearAllLines();
    const transform = e.transform;
    const corner = transform.corner;
    this.corner = corner;
    this.activeObj = e.target;
    let objects = getVisibleObjects(this.canvas);
    const target = e.target;
    const parent = Finder.findParentGroup(this.canvas, target._parent_id_);
    if (parent) {
      objects = parent.getObjects();
    }
    const activeObjects = this.canvas.getActiveObjects();
    const allObjects: Array<FabricObject> = [];
    const traverse = (root: FabricObject) => {
      // 排除active
      if (activeObjects.includes(root)) return;
      if (root._name_ === 'railing') return;
      // 只包含真实元素且通过校验的图形
      if (!isRealElement(root)) return;

      allObjects.push(root);

      if (root instanceof Group) {
        root.getObjects().forEach(traverse);
      }
    };
    // 遍历取出所有对象
    objects.forEach(traverse);
    const { activeObject } = this.traverseAllObjects(this.activeObj, allObjects);
    this.snapScale({
      activeObject,
    })
  }

  private handleMouseUp = () => {
    if (this.isScale) {
      this.activeObj?.setCoords();
      this.activeObj?.set({
        originX: 'center',
        originY: 'center',
        left: this.activeObj.getCenterPoint().x,
        top: this.activeObj.getCenterPoint().y,
      })
    }
    this.isScale = false;
    this.corner = undefined;
    if (this.horizontalLines.length || this.verticalLines.length) {
      this.clearGuideline();
      this.clearAllLines();
    }
  };

  // 清空所有的辅助线
  private clearAllLines = () => {
    this.verticalLines.length = this.horizontalLines.length = 0;
  };

  private objectMoving({ target }: { target: FabricObject }) {
    this.clearAllLines();

    const transform = this.canvas._currentTransform;
    if (!transform) return;
    this.activeObj = target;
    // 获取active的对象
    const activeObjects = this.canvas.getActiveObjects();
    // 获取视口内的所有对象
    const objects = getVisibleObjects(this.canvas);

    const allObjects: Array<FabricObject> = [];
    const traverse = (root: FabricObject) => {
      // 排除active
      if (activeObjects.includes(root)) return;
      if (root._name_ === 'railing') return;
      // 只包含真实元素且通过校验的图形
      if (!isRealElement(root)) return;

      allObjects.push(root);

      if (root instanceof Group) {
        root.getObjects().forEach(traverse);
      }
    };
    // 遍历取出所有对象
    objects.forEach(traverse);
    const { activeObject, draggingObjCoords, snapXPoints, snapYPoints } = this.traverseAllObjects(target, allObjects);
    this.snap({
      activeObject,
      draggingObjCoords,
      snapXPoints,
      snapYPoints,
    });
  }

  // 遍历所有对象 计算坐标 放入辅助线
  private traverseAllObjects = (
    activeObject: FabricObject,
    canvasObjects: Array<FabricObject>,
  ) => {
    const activeObjectCoords = this.getAllCoords(activeObject);
    const snapXPoints: Set<number> = new Set();
    const snapYPoints: Set<number> = new Set();
    for (let i = 0; i < canvasObjects.length; i++) {
      const canvasObj = canvasObjects[i];
      const objCoords = {
        ...canvasObj.aCoords,
        c: canvasObj.getCenterPoint(),
      } as ACoordsAppendCenter;
      this.processHorizontalLines(
        activeObjectCoords,
        objCoords,
        snapYPoints,
        canvasObj.width,
      );
      this.processVerticalLines(
        activeObjectCoords,
        objCoords,
        snapXPoints,
        canvasObj.height,
      );
    }
    return {
      activeObject,
      draggingObjCoords: activeObjectCoords,
      snapXPoints,
      snapYPoints,
    }
  };

  // 处理水平线
  private processHorizontalLines(
    activeObjectCoords: ACoordsAppendCenter,
    objCoords: ACoordsAppendCenter,
    snapYPoints: Set<number>,
    objWidth: number,
  ) {
    getKeys(activeObjectCoords).forEach((activeObjPoint) => {
      const newCoords = objCoords;
      function calcHorizontalLineCoords(
        objPoint: keyof ACoordsAppendCenter,
        activeObjCoords: ACoordsAppendCenter
      ) {
        let x1: number, x2: number;
        if (objPoint === "c") {
          x1 = Math.min(
            objCoords.c.x - objWidth / 2,
            activeObjCoords[activeObjPoint].x
          );
          x2 = Math.max(
            objCoords.c.x + objWidth / 2,
            activeObjCoords[activeObjPoint].x
          );
        } else {
          x1 = Math.min(
            objCoords[objPoint].x,
            activeObjCoords[activeObjPoint].x
          );
          x2 = Math.max(
            objCoords[objPoint].x,
            activeObjCoords[activeObjPoint].x
          );
        }
        return { x1, x2 };
      }
      getKeys(newCoords).forEach((objPoint) => {
        if (
          this.isInRange(
            activeObjectCoords[activeObjPoint].y,
            objCoords[objPoint].y
          )
        ) {
          const y = objCoords[objPoint].y;
          if (activeObjectCoords.c) {
            const offset = activeObjectCoords[activeObjPoint].y - y;
            snapYPoints.add(activeObjectCoords.c.y - offset);
          }
          const { x1, x2 } = calcHorizontalLineCoords(
            objPoint,
            activeObjectCoords as ACoordsAppendCenter
          );
          this.horizontalLines.push({ y, x1, x2 });
        }
      });
    });
  }

  // 处理垂直线
  private processVerticalLines(
    activeObjectCoords: ACoordsAppendCenter,
    objCoords: ACoordsAppendCenter,
    snapXPoints: Set<number>,
    objHeight: number,
  ) {
    getKeys(activeObjectCoords).forEach((activeObjPoint) => {
      const newCoords = objCoords;
      function calcVerticalLineCoords(
        objPoint: keyof ACoordsAppendCenter,
        activeObjCoords: ACoordsAppendCenter
      ) {
        let y1: number, y2: number;
        if (objPoint === "c") {
          y1 = Math.min(
            newCoords.c.y - objHeight / 2,
            activeObjCoords[activeObjPoint].y
          );
          y2 = Math.max(
            newCoords.c.y + objHeight / 2,
            activeObjCoords[activeObjPoint].y
          );
        } else {
          y1 = Math.min(
            objCoords[objPoint].y,
            activeObjCoords[activeObjPoint].y
          );
          y2 = Math.max(
            objCoords[objPoint].y,
            activeObjCoords[activeObjPoint].y
          );
        }
        return { y1, y2 };
      }

      getKeys(newCoords).forEach((objPoint) => {
        if (!activeObjectCoords[activeObjPoint]) return;
        if (
          this.isInRange(
            activeObjectCoords[activeObjPoint].x,
            objCoords[objPoint].x
          )
        ) {
          const x = objCoords[objPoint].x;

          if (activeObjectCoords.c) {
            const offset = activeObjectCoords[activeObjPoint].x - x;
            snapXPoints.add(activeObjectCoords.c.x - offset);
          }

          const { y1, y2 } = calcVerticalLineCoords(
            objPoint,
            activeObjectCoords as ACoordsAppendCenter
          );
          this.verticalLines.push({ x, y1, y2 });
        }
      });
    });
  }

  // 获取对象的坐标
  private getAllCoords(obj: FabricObject) {
    const positions = getPositions(this.corner);
    obj.setCoords();
    const [tl, tr, br, bl] = obj.getCoords();
    const result = { tl, tr, br, bl, c: obj.getCenterPoint() }
    Object.keys(result).forEach((key) => {
      if (!positions.includes(key as keyof ACoordsAppendCenter)) {
        delete result[key as keyof ACoordsAppendCenter];
      }
    })
    return result;
  }

  // 是否在指定范围内
  private isInRange = (value1: number, value2: number) => {
    return (
      Math.abs(Math.round(value1) - Math.round(value2)) <=
      this.aligningLineMargin / this.canvas.getZoom()
    );
  };

  // 自动吸附对象
  private snap({
    activeObject,
    draggingObjCoords,
    snapXPoints,
    snapYPoints,
  }: {
    /** 当前活动对象 */
    activeObject: FabricObject;
    /** 活动对象的坐标 */
    draggingObjCoords: ACoordsAppendCenter;
    /** 横向吸附点列表 */
    snapXPoints: Set<number>;
    /** 纵向吸附点列表 */
    snapYPoints: Set<number>;
  }) {
    if (snapXPoints.size === 0 && snapYPoints.size === 0) return;

    // 获得最近的吸附点
    const sortPoints = (list: Set<number>, originPoint: number): number => {
      if (list.size === 0) {
        return originPoint;
      }

      const sortedList = [...list].sort(
        (a, b) => Math.abs(originPoint - a) - Math.abs(originPoint - b)
      );

      return sortedList[0];
    };

    activeObject.setXY(
      new Point(
        sortPoints(snapXPoints, draggingObjCoords.c.x),
        sortPoints(snapYPoints, draggingObjCoords.c.y)
      ),
      "center",
      "center"
    );
  }

  // 缩放时动态吸附并保证等比缩放
  private snapScale({
    activeObject,
  }: {
    activeObject: FabricObject;
  }) {
    const transform = this.canvas._currentTransform;
    if (!transform) return;
    const corner = transform.corner;
    const currentPoint = activeObject.aCoords[corner as keyof TCornerPoint]
    const cornetData = cornerMapping[corner](activeObject)
    const snapScale = getSnapScaleFromNearestLines({
      dragPoint: currentPoint,
      anchorPoint: cornetData.anchorPoint,
      verticalLines: this.verticalLines,
      horizontalLines: this.horizontalLines,
      scaleX: activeObject.scaleX,
      scaleY: activeObject.scaleY,
    })
    if (activeObject.originX !== cornetData.originX || activeObject.originY !== cornetData.originY) {
      activeObject.set({
        originX: cornetData.originX,
        originY: cornetData.originY,
        left: cornetData.left,
        top: cornetData.top,
      })
    }
    if (snapScale) {
      activeObject.set({
        scaleX: Math.abs(snapScale),
        scaleY: Math.abs(snapScale),
      })
    }
  }

  // 绘制点
  private drawSign(x: number, y: number) {
    const ctx = this.canvas.getTopContext();

    ctx.strokeStyle = this.aligningLineColor;
    ctx.beginPath();

    const size = 3;
    ctx.moveTo(x - size, y - size);
    ctx.lineTo(x + size, y + size);
    ctx.moveTo(x + size, y - size);
    ctx.lineTo(x - size, y + size);
    ctx.stroke();
  }

  // 绘制线
  private drawLine(x1: number, y1: number, x2: number, y2: number) {
    const ctx = this.canvas.getTopContext();
    const point1 = new Point(x1, y1).transform(this.canvas.viewportTransform);
    const point2 = new Point(x2, y2).transform(this.canvas.viewportTransform);
    ctx.save();
    ctx.lineWidth = this.aligningLineWidth;
    ctx.strokeStyle = this.aligningLineColor;
    ctx.beginPath();

    ctx.moveTo(point1.x, point1.y);
    ctx.lineTo(point2.x, point2.y);

    ctx.stroke();

    this.drawSign(point1.x, point1.y);
    this.drawSign(point2.x, point2.y);

    ctx.restore();

    this.dirty = true;
  }

  // 绘制垂直线
  private drawVerticalLine(
    coords: VerticalLineCoords,
    movingCoords: ACoordsAppendCenter
  ) {
    if (
      !Object.values(movingCoords).some(
        (coord) => Math.abs(coord.x - coords.x) < 0.0001
        // (coord) => Math.abs(coord.x - coords.x) < 1
      )
    )return;
    this.drawLine(
      coords.x,
      Math.min(coords.y1, coords.y2),
      coords.x,
      Math.max(coords.y1, coords.y2)
    );
  }

  // 绘制水平线
  private drawHorizontalLine(
    coords: HorizontalLineCoords,
    movingCoords: ACoordsAppendCenter
  ) {
    if (
      !Object.values(movingCoords).some(
        (coord) => Math.abs(coord.y - coords.y) < 0.0001
        // (coord) => Math.abs(coord.y - coords.y) < 1
      )
    )
      return;
    this.drawLine(
      Math.min(coords.x1, coords.x2),
      coords.y,
      Math.max(coords.x1, coords.x2),
      coords.y
    );
  }

  // 绘制对齐线
  private drawGuideLines(e: { ctx: CanvasRenderingContext2D | null }) {
    if (
      !e.ctx ||
      (!this.verticalLines.length && !this.horizontalLines.length) ||
      !this.activeObj
    ) {
      return;
    }
    const movingCoords = this.getAllCoords(this.activeObj);
    for (let i = this.verticalLines.length; i--;) {
      this.drawVerticalLine(this.verticalLines[i], movingCoords);
    }
    for (let i = this.horizontalLines.length; i--;) {
      this.drawHorizontalLine(this.horizontalLines[i], movingCoords);
    }
    // this.canvas.calcOffset()
  }

  // 清空对齐线
  private clearGuideline() {
    if (!this.dirty) return;
    this.dirty = false;
    this.canvas.clearContext(this.canvas.getTopContext());
  }

  // 销毁
  public dispose() {
    this.canvas.off(this.canvasEvents);
  }
}
