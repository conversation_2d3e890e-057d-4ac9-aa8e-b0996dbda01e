import { z } from "zod/v4";
import { controlRotateSchema } from "../../schema/control";
import { InteractiveFabricObject } from "node_modules/fabric/dist/src/shapes/Object/InteractiveObject";
import { Control } from "fabric";
import { TPointerEvent } from "fabric";
import { svgToBase64 } from "../../../../utils";
import { ownDefaultsMouseStyle } from "../../../../base/ownDefaults";

const applyArray = ['image', 'text', 'container', 'video']

export const WheeRotateTheme: Array<z.infer<typeof controlRotateSchema>> = [
    {
        key: 'mtr',
        show: false,
        shape: 'rectangle',
        xSize: 8,
        ySize: 8,
        offsetX: 6,
        offsetY: -12,
        backgroundColor: '#FFFFFF',
        icon: '',
        strokeWidth: 1,
        radius: {
            lt: 4,
            lr: 4,
            bl: 4,
            br: 4,
        },
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => {
            return `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtrHandler(fabricObject ? fabricObject.angle : _eventData as unknown as number))}) 16 16, auto`
        }
    },
    {
        key: 'mtl',
        show: false,
        shape: 'rectangle',
        xSize: 8,
        ySize: 8,
        offsetX: -12,
        offsetY: -12,
        backgroundColor: '#FFFFFF',
        icon: '',
        strokeWidth: 1,
        radius: {
            lt: 4,
            lr: 4,
            bl: 4,
            br: 4,
        },
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject ? fabricObject.angle : _eventData as unknown as number))}) 16 16, auto`
    },
    {
        key: 'mbl',
        show: false,
        shape: 'rectangle',
        xSize: 8,
        ySize: 8,
        offsetX: -6,
        offsetY: 12,
        backgroundColor: '#FFFFFF',
        icon: '',
        strokeWidth: 1,
        radius: {
            lt: 4,
            lr: 4,
            bl: 4,
            br: 4,
        },
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mblHandler(fabricObject ? fabricObject.angle : _eventData as unknown as number))}) 16 16, auto`
    },
    {
        key: 'mbr',
        show: false,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: 6,
        offsetY: 12,
        backgroundColor: '#FFFFFF',
        icon: '',
        strokeWidth: 1,
        radius: {
            lt: 4,
            lr: 4,
            bl: 4,
            br: 4,
        },
        apply: applyArray,
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mbrHandler(fabricObject ? fabricObject.angle : _eventData as unknown as number))}) 16 16, auto`
    },

]
