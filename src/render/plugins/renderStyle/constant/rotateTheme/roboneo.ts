import { z } from "zod/v4";
import { controlRotateSchema } from "../../schema/control";
import { InteractiveFabricObject } from "node_modules/fabric/dist/src/shapes/Object/InteractiveObject";
import { Control } from "fabric";
import { TPointerEvent } from "fabric";
import { svgToBase64 } from "../../../../utils";
import { ownDefaultsMouseStyle } from "../../../../base/ownDefaults";


const applyArray = ['image', 'text', 'container', 'video']

export const RoboneoRotateTheme: Array<z.infer<typeof controlRotateSchema>> = [
    {
        key: 'mbm',
        show: true,
        shape: 'ellipse',
        xSize: 8,
        ySize: 8,
        offsetX: 0,
        offsetY: 8,
        backgroundColor: '#FFFFFF',
        icon: '',
        apply: applyArray,
        strokeWidth: 1,
        radius: {
            lt: 4,
            lr: 4,
            bl: 4,
            br: 4,
        },
        cursor: (
            _eventData: TPointerEvent,
            _control: Control,
            fabricObject: InteractiveFabricObject,
        ) => {
            return `url(${svgToBase64(ownDefaultsMouseStyle.rotateHandler.mtlHandler(fabricObject.angle))}) 16 16, auto`
        }
    },
]
