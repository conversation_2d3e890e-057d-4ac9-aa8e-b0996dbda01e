import { ShapeTypeSchema } from "../../../base/schema";
import {} from "../../../utils/types/"
import { z } from "zod/v4";


export const shapeSchema = z.enum(['ellipse', 'rectangle']);

export const rotateControlsSchema = z.enum(['mtr', 'mtl', 'mbl', 'mbr', 'mtm', 'mbm', 'mlm', 'mrm']);

export const scaleControlsSchema = z.enum(['tl', 'tr', 'bl', 'br', 'ml', 'mr', 'mt', 'mb']);

export const controlRotateShowByShapeSchema = z.array(ShapeTypeSchema).optional().default(['image', 'text', 'container',]).describe('那些形状显示旋转控制点');

export const radius = z.object({
    lt: z.number().optional().default(0),
    lr: z.number().optional().default(0),
    bl: z.number().optional().default(0),
    br: z.number().optional().default(0),
}).optional().default({
    lt: 0,
    lr: 0,
    bl: 0,
    br: 0,
})

export const controlPointSchema = z.object({
    key: scaleControlsSchema,
    shape: shapeSchema.optional().default('ellipse'),
    xSize: z.number().optional().default(8),
    ySize: z.number().optional().default(8),
    backgroundColor: z.string().optional().default('#FFFFFF'),
    icon: z.string().optional().default(''),
    apply: z.array(z.string()).optional().default([]),
    strokeWidth: z.number().optional().default(1),
    offsetX: z.number().optional().default(0),
    offsetY: z.number().optional().default(0),
    radius,
    cursor: z.any()
});

export const controlRotateSchema = z.object({
    key: rotateControlsSchema,
    show: z.boolean().optional().default(true),
    shape: shapeSchema.optional().default('ellipse'),
    xSize: z.number().optional().default(8),
    ySize: z.number().optional().default(8),
    offsetX: z.number().optional().default(0),
    offsetY: z.number().optional().default(0),
    backgroundColor: z.string().optional().default('#FFFFFF'),
    icon: z.string().optional().default(''),
    apply: z.array(z.string()).optional().default([]),
    strokeWidth: z.number().optional().default(1),
    radius,
    cursor: z.any()
})

export const rotateRenderControlsSchema = z.array(controlRotateSchema)

export const scaleRenderControlsSchema = z.array(controlPointSchema)
