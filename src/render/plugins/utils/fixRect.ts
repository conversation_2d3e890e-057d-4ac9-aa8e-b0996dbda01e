import { FixedLayout, Group, LayoutManager } from "fabric";
import { Render } from "../../render";
import { z } from "zod";
import { IImage } from "../../base/package/image/image";

const RectOptionsSchema = z.object({
    width: z.number(),
    height: z.number(),
    left: z.number(),
    top: z.number(),
    minWidth: z.number().optional().default(1),
    maxWidth: z.number(),
    minHeight: z.number().optional().default(1),
    maxHeight: z.number(),
    fill: z.string().optional().default('#000'),
    opacity: z.number().optional().default(0.5),
    name: z.string(),
})


export function getTargetBounds (this: IImage) {
    const aCoords = this.aCoords
    const positions = Object.values(aCoords)
    const left = Math.min(...positions.map(item => item.x))
    const top = Math.min(...positions.map(item => item.y))
    const right = Math.max(...positions.map(item => item.x))
    const bottom = Math.max(...positions.map(item => item.y))
    return {
        left,
        top,
        right,
        bottom,
    }
}

export function getFixAreaBounds (this: Group) {
    const aCoords = this.aCoords
    const positions = Object.values(aCoords)
    const left = Math.min(...positions.map(item => item.x))
    const top = Math.min(...positions.map(item => item.y))
    const right = Math.max(...positions.map(item => item.x))
    const bottom = Math.max(...positions.map(item => item.y))
    return {
        left,
        top,
        right,
        bottom,
    }
}

export function createRect(this: Render, options: z.infer<typeof RectOptionsSchema> & { target: IImage }) {
    const result = RectOptionsSchema.parse(options)
    const target = new Group([], {
        layoutManager: new LayoutManager(new FixedLayout()),
        width: result.width,
        height: result.height,
        left: result.left,
        top: result.top,
        backgroundColor: result.fill,
        opacity: result.opacity,
    })

    target.set('maxWidth', result.maxWidth)
    target.set('minWidth', result.minWidth)
    target.set('maxHeight', result.maxHeight)
    target.set('minHeight', result.minHeight)
    target.setControlVisible("mt", true);
    target.setControlVisible("mt", true);
    target.setControlVisible("mb", true);
    target.setControlVisible("mb", true);
    target.set("_name_", result.name);
    this._FC.discardActiveObject();
    this._FC.add(target)
    return {
        target,
    }
}