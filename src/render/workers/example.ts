/**
 * Worker使用示例
 */
import { 
    worker<PERSON>anager, 
    gifWorkerUtils, 
    processGifWithWorker,
    dynamicPictureToFrame 
} from '../index'

// 示例1: 使用WorkerManager处理GIF
export async function example1_WorkerManager() {
    try {
        console.log('=== 示例1: 使用WorkerManager ===')
        
        // 1. 初始化GIF worker（可选，会自动初始化）
        await workerManager.initializeWorker('gif', '/worker/gif-worker.iife.js')
        
        // 2. 加载GIF数据
        const gifUrl = 'https://example.com/animation.gif'
        const gifData = await workerManager.loadGifData(gifUrl)
        
        console.log('GIF信息:', {
            frameCount: gifData.frames.length,
            dimensions: gifData.dims,
            firstFrameDelay: gifData.frames[0]?.delay
        })
        
        // 3. 转换为Blob URLs
        const blobUrls = gifData.frames.map(frame => 
            URL.createObjectURL(frame.frameBlob)
        )
        
        console.log('生成的Blob URLs:', blobUrls.length)
        
        // 4. 清理资源
        blobUrls.forEach(url => URL.revokeObjectURL(url))
        
        return gifData
    } catch (error) {
        console.error('示例1失败:', error)
        throw error
    }
}

// 示例2: 使用GifWorkerUtils（推荐）
export async function example2_GifWorkerUtils() {
    try {
        console.log('=== 示例2: 使用GifWorkerUtils ===')
        
        const gifUrl = 'https://example.com/animation.gif'
        
        // 一步完成GIF处理
        const { blobUrls, gifData } = await processGifWithWorker(
            gifUrl,
            '/worker/gif-worker.iife.js' // 可选
        )
        
        // 获取GIF信息
        const info = gifWorkerUtils.getGifInfo(gifData)
        console.log('GIF详细信息:', info)
        
        // 使用Blob URLs（比如创建img元素）
        const imgElements = blobUrls.map((url, index) => {
            const img = document.createElement('img')
            img.src = url
            img.alt = `Frame ${index}`
            return img
        })
        
        console.log('创建的图片元素:', imgElements.length)
        
        // 清理资源
        gifWorkerUtils.revokeBlobUrls(blobUrls)
        
        return { blobUrls, gifData, imgElements }
    } catch (error) {
        console.error('示例2失败:', error)
        throw error
    }
}

// 示例3: 使用现有的dynamicPictureToFrame函数（带worker支持）
export async function example3_DynamicPictureToFrame() {
    try {
        console.log('=== 示例3: 使用dynamicPictureToFrame ===')
        
        const gifUrl = 'https://example.com/animation.gif'
        
        // 使用worker处理
        const framesWithWorker = await dynamicPictureToFrame(gifUrl, {
            useWorker: true,
            workerUrl: '/worker/gif-worker.iife.js'
        })
        
        console.log('使用worker处理的帧数:', framesWithWorker?.length || 0)
        
        // 不使用worker处理（传统方式）
        const framesWithoutWorker = await dynamicPictureToFrame(gifUrl, {
            useWorker: false
        })
        
        console.log('不使用worker处理的帧数:', framesWithoutWorker?.length || 0)
        
        return { framesWithWorker, framesWithoutWorker }
    } catch (error) {
        console.error('示例3失败:', error)
        throw error
    }
}

// 示例4: 图像处理worker
export async function example4_ImageWorker() {
    try {
        console.log('=== 示例4: 使用ImageWorker ===')
        
        // 创建测试用的ImageData
        const canvas = document.createElement('canvas')
        canvas.width = 100
        canvas.height = 100
        const ctx = canvas.getContext('2d')!
        
        // 绘制一些测试内容
        ctx.fillStyle = 'red'
        ctx.fillRect(0, 0, 50, 50)
        ctx.fillStyle = 'blue'
        ctx.fillRect(50, 50, 50, 50)
        
        const imageData = ctx.getImageData(0, 0, 100, 100)
        
        // 检测是否有透明通道
        const hasTransparent = await workerManager.isImageDataHasTransparent(imageData)
        console.log('是否有透明通道:', hasTransparent)
        
        // 图像数据反转
        const invertedData = await workerManager.imageDataInvertByMaskImage(
            imageData, 
            [255, 0, 0] // 红色
        )
        console.log('反转处理完成')
        
        return { originalData: imageData, invertedData }
    } catch (error) {
        console.error('示例4失败:', error)
        throw error
    }
}

// 示例5: 错误处理和降级方案
export async function example5_ErrorHandling() {
    try {
        console.log('=== 示例5: 错误处理和降级 ===')
        
        const gifUrl = 'https://example.com/animation.gif'
        
        // 尝试使用worker，失败时降级
        async function loadGifWithFallback(url: string) {
            try {
                // 尝试使用worker
                console.log('尝试使用worker处理...')
                return await gifWorkerUtils.loadGifWithWorker(url, '/worker/gif-worker.iife.js')
            } catch (workerError) {
                console.warn('Worker失败，使用主线程处理:', workerError)
                
                // 降级到主线程处理
                const frames = await dynamicPictureToFrame(url, { useWorker: false })
                if (frames && frames.length > 0) {
                    // 转换为worker格式
                    return {
                        frames: frames.map(() => ({ frameBlob: new Blob(), delay: 100 })),
                        dims: { width: 100, height: 100 }
                    }
                }
                throw new Error('主线程处理也失败了')
            }
        }
        
        const result = await loadGifWithFallback(gifUrl)
        console.log('最终结果:', result)
        
        return result
    } catch (error) {
        console.error('示例5失败:', error)
        throw error
    }
}

// 示例6: 资源管理
export async function example6_ResourceManagement() {
    try {
        console.log('=== 示例6: 资源管理 ===')
        
        // 检查worker状态
        console.log('GIF Worker是否就绪:', gifWorkerUtils.isGifWorkerReady())
        console.log('已初始化的workers:', workerManager.getInitializedWorkers())
        
        // 处理多个GIF
        const urls = [
            'https://example.com/gif1.gif',
            'https://example.com/gif2.gif',
            'https://example.com/gif3.gif'
        ]
        
        const results = []
        for (const url of urls) {
            try {
                const { blobUrls, gifData } = await processGifWithWorker(url)
                results.push({ url, blobUrls, gifData })
                console.log(`处理完成: ${url}`)
            } catch (error) {
                console.error(`处理失败: ${url}`, error)
            }
        }
        
        // 批量清理资源
        for (const result of results) {
            gifWorkerUtils.revokeBlobUrls(result.blobUrls)
        }
        
        // 销毁所有worker
        await workerManager.destroyWorker() // 销毁所有
        // 或者只销毁特定worker
        // await workerManager.destroyWorker('gif')
        
        console.log('资源清理完成')
        
        return results
    } catch (error) {
        console.error('示例6失败:', error)
        throw error
    }
}

// 运行所有示例
export async function runAllExamples() {
    console.log('开始运行所有Worker示例...')
    
    try {
        await example1_WorkerManager()
        await example2_GifWorkerUtils()
        await example3_DynamicPictureToFrame()
        await example4_ImageWorker()
        await example5_ErrorHandling()
        await example6_ResourceManagement()
        
        console.log('所有示例运行完成！')
    } catch (error) {
        console.error('示例运行失败:', error)
    }
}
