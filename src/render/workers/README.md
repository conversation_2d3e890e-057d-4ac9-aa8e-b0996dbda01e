# Worker 使用指南

## 概述

render包现在支持独立的worker文件，可以按需加载不同功能的worker，避免包体积过大。

## Worker类型

### 1. Image Worker (`image-worker.iife.js`)
处理图像相关操作：
- `imageDataInvertByMaskImage` - 图像数据反转
- `isImageDataHasTransparent` - 检测透明通道
- `hasAlpha` - 检测Alpha通道
- `upscaleReplaceAlpha` - 放大替换Alpha
- `cutoutWithMask` - 遮罩抠图

### 2. GIF Worker (`gif-worker.iife.js`)
处理GIF相关操作：
- `loadGifData` - 加载GIF数据
- `composeFullFrames` - 组合完整帧
- `fullFrameToBlob` - 转换为Blob

## 使用方式

### 方式一：使用WorkerManager（推荐）

```typescript
import { workerManager } from '@meitu/whee-infinite-canvas'

// 1. 初始化特定类型的worker
await workerManager.initializeWorker('gif', './worker/gif-worker.iife.js')

// 2. 使用GIF功能
const gifData = await workerManager.loadGifData('https://example.com/animation.gif')

// 3. 使用图像处理功能（会自动初始化image worker）
const result = await workerManager.cutoutWithMask(imageData, maskData)
```

### 方式二：使用GifWorkerUtils（专门处理GIF）

```typescript
import { gifWorkerUtils, processGifWithWorker } from '@meitu/whee-infinite-canvas'

// 完整的GIF处理流程
const { blobUrls, gifData } = await processGifWithWorker(
    'https://example.com/animation.gif',
    './worker/gif-worker.iife.js' // 可选，不提供会自动检测
)

// 使用Blob URLs
console.log('GIF帧数:', blobUrls.length)
console.log('GIF尺寸:', gifData.dims)

// 记得释放内存
gifWorkerUtils.revokeBlobUrls(blobUrls)
```

### 方式三：直接使用workerpool

```typescript
import workerpool from 'workerpool'

// 创建GIF worker pool
const gifPool = workerpool.pool('./worker/gif-worker.iife.js')

// 调用函数
const result = await gifPool.exec('loadGifData', ['https://example.com/animation.gif'])

// 清理
await gifPool.terminate()
```

## NPM包导入

```typescript
// 主包
import { WorkerManager, gifWorkerUtils } from '@meitu/whee-infinite-canvas'

// Worker文件（需要手动引入到你的项目中）
// 从 node_modules/@meitu/whee-infinite-canvas/worker/ 复制到你的public目录
```

## 在项目中集成

### 1. 复制Worker文件

```bash
# 复制worker文件到你的public目录
cp node_modules/@meitu/whee-infinite-canvas/worker/*.js public/worker/
```

### 2. 使用示例

```typescript
import { gifWorkerUtils } from '@meitu/whee-infinite-canvas'

class GifProcessor {
    async processGif(url: string) {
        try {
            // 使用相对路径引用worker文件
            const { blobUrls, gifData } = await gifWorkerUtils.processGif(
                url, 
                '/worker/gif-worker.iife.js'
            )
            
            console.log(`处理完成: ${blobUrls.length}帧`)
            return blobUrls
        } catch (error) {
            console.error('处理失败:', error)
            throw error
        }
    }
    
    cleanup() {
        // 销毁worker释放资源
        gifWorkerUtils.destroyGifWorker()
    }
}
```

## 自动检测Worker路径

WorkerManager会尝试自动检测worker文件路径：

1. `./worker/[worker-type]-worker.iife.js`
2. `../worker/[worker-type]-worker.iife.js`
3. `../../worker/[worker-type]-worker.iife.js`
4. `/worker/[worker-type]-worker.iife.js`

如果自动检测失败，请手动指定worker文件路径。

## 错误处理

```typescript
try {
    await workerManager.initializeWorker('gif', '/path/to/gif-worker.iife.js')
    const result = await workerManager.loadGifData(url)
} catch (error) {
    if (error.message.includes('未初始化')) {
        console.error('Worker未正确初始化')
    } else if (error.message.includes('无法找到')) {
        console.error('Worker文件路径错误')
    } else {
        console.error('处理过程中出错:', error)
    }
}
```

## 性能优化建议

1. **按需加载**: 只初始化需要的worker类型
2. **复用Worker**: 避免频繁创建和销毁worker
3. **内存管理**: 及时释放Blob URLs
4. **错误恢复**: 实现worker失败时的降级方案

```typescript
// 降级方案示例
async function loadGifWithFallback(url: string) {
    try {
        // 尝试使用worker
        return await gifWorkerUtils.loadGifWithWorker(url)
    } catch (error) {
        console.warn('Worker失败，使用主线程处理:', error)
        // 降级到主线程处理
        return await loadGifInMainThread(url)
    }
}
```
