import { ExportElementToImageOptions } from "../../base/types";
import { IText} from "fabric";
import { Group } from "fabric";
import { merge } from "lodash-es";
import { createElement, getElementOptions } from "../shapes";
import { Render } from "../../render";
import { ElementName } from "../enum";
import { FrameElementOptions, ContainerElementOptions } from "../types";

/**
 * 导出元素
 * @param this
 * @param element
 * @param options
 * @returns
 */
export async function createExportElement(
    this: Render,
    elements: Array<Group | IText> | Group | IText,
    options: ExportElementToImageOptions
) {
    const defaultOptions: ExportElementToImageOptions = {
        includeType: [],
        callBack: undefined,
        exportType: 'png',
        quality: 1,
        multiplier: 2,
        merge: false,
        applyParentScale: false
    };
    if (!Array.isArray(elements)) {
        elements = [elements];
    }
    options = merge(defaultOptions, options);
    const elementOptions = elements.map(element => getElementOptions.call(this, element));

    const elementPromises = elementOptions.map(element => {
        if (
            element._name_ === ElementName.IMAGE ||
            element._name_ === ElementName.TEXT
        ) {
            if (options.callBack) {
                return createElement(options.callBack(element));
            }
            return createElement(element);
        } else {
            let result = element;
            if (
                element._name_ === ElementName.CONTAINER ||
                element._name_ === ElementName.FRAME
            ) {
                const fn = (targetOptions: FrameElementOptions | ContainerElementOptions) => {
                    if (!targetOptions.children) return targetOptions;
                    targetOptions.children = targetOptions.children.map(child => {
                        if (
                            !(
                                child._name_ === ElementName.CONTAINER ||
                                options.includeType.includes(child._name_ as ElementName.IMAGE | ElementName.TEXT)
                            )
                        ) {
                            child.opacity = 0;
                        }
                        return child;
                    });
                    targetOptions.children.forEach(child => {
                        if (
                            (child._name_ === ElementName.CONTAINER || child._name_ === ElementName.FRAME) &&
                            (child as FrameElementOptions | ContainerElementOptions).children
                        ) {
                            const childOptions = child as FrameElementOptions | ContainerElementOptions;
                            childOptions.children = fn(childOptions).children;
                        }
                    });

                    return targetOptions;
                }
                result = fn(element as FrameElementOptions | ContainerElementOptions);
            }
            if (options.callBack) {
                result = options.callBack(element);
            }
            return createElement(result);
        }
    })
    const shapes = await Promise.all(elementPromises);
    if (shapes.length > 1 && options.merge) {
        const group = new Group();
        group.add(...shapes);
        return [group];
    }
    return shapes;
}
