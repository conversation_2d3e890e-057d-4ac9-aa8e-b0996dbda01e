import type { ParsedFrame } from "gifuct-js"

type FullFrame = {
  data: Uint8ClampedArray;
  delay: number;
  dims: { width: number; height: number };
};

type CachedData = {
    frames: Array<{
        frameBlob: Blob;
        delay: number;
    }>;
    dims: {
        width: number;
        height: number;
    };
}

export function composeFullFrames(frames: ParsedFrame[]): FullFrame[] {
  const { width, height } = frames[0].dims;
  const canvasSize = width * height * 4;
  const result: FullFrame[] = [];

  const canvas = new Uint8ClampedArray(canvasSize); // 当前完整帧画布
  const previousCanvas = new Uint8ClampedArray(canvasSize); // 用于 disposalType=3

  for (let i = 0; i < frames.length; i++) {
    const frame = frames[i];
    const { dims, pixels, colorTable, transparentIndex, disposalType } = frame;

    // 保存前一帧（仅在 disposalType=3 时使用）
    if (disposalType === 3) {
      previousCanvas.set(canvas);
    }

    // 叠加 patch 区域到当前 canvas
    for (let y = 0; y < dims.height; y++) {
      for (let x = 0; x < dims.width; x++) {
        const dstX = dims.left + x;
        const dstY = dims.top + y;
        const canvasIdx = (dstY * width + dstX) * 4;
        const pixelIdx = y * dims.width + x;
        const colorIdx = pixels[pixelIdx];

        if (colorIdx !== transparentIndex) {
          const [r, g, b] = colorTable[colorIdx];
          canvas[canvasIdx + 0] = r;
          canvas[canvasIdx + 1] = g;
          canvas[canvasIdx + 2] = b;
          canvas[canvasIdx + 3] = 255;
        }
      }
    }

    // 保存当前完整帧（复制一份）
    result.push({
      data: new Uint8ClampedArray(canvas),
      delay: frame.delay,
      dims: { width, height },
    });

    // 处理 disposalType
    switch (disposalType) {
      case 2: // Clear to background
        for (let y = 0; y < dims.height; y++) {
          for (let x = 0; x < dims.width; x++) {
            const idx = ((dims.top + y) * width + (dims.left + x)) * 4;
            canvas[idx + 0] = 0;
            canvas[idx + 1] = 0;
            canvas[idx + 2] = 0;
            canvas[idx + 3] = 0;
          }
        }
        break;
      case 3: // Restore to previous
        canvas.set(previousCanvas);
        break;
      case 0: // Unspecified
      case 1: // Do not dispose
      default:
        // 保留当前画布
        break;
    }
  }

  return result;
}


export async function fullFrameToBlob(frames: FullFrame[]): Promise<CachedData> {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) throw new Error('Cannot get 2D context');

  const resultData: CachedData = {
    frames: [],
    dims: {
        width: frames[0].dims.width,
        height: frames[0].dims.height,
    }
  };

  for (const frame of frames) {
    const { data, dims } = frame;
    const { width, height } = dims;

    canvas.width = width;
    canvas.height = height;

    const imageData = new ImageData(data, width, height);
    ctx.putImageData(imageData, 0, 0);

    const blob = await new Promise<Blob>((resolve) =>
      canvas.toBlob((b) => resolve(b!), 'image/png')
    );
    resultData.frames.push({
        delay: frame.delay,
        frameBlob: blob
    });
  }
  return resultData;
}
