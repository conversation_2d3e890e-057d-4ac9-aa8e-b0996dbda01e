import Logger from "@/render/logger";
import { GifCacheManager } from "../../base/package/dynamic-picture/utils/gif-cache-manager";

export async function srcToArrayBuffer(src: string): Promise<ArrayBuffer> {
    const response = await fetch(src);
    if (!response.ok) {
        throw new Error(`获取资源失败: ${response.status} ${response.statusText}`);
    }
    return await response.arrayBuffer();
}

export async function dynamicPictureToFrame(src: string) {
    try {
        Logger.info("加载GIF缓存数据")
        const gifCacheManager = await GifCacheManager.getInstance();
        const cachedData = await gifCacheManager.createBlobUrl(src);
        if (cachedData && cachedData.length) return cachedData;
    } catch (error) {
        Logger.error("加载帧数据异常：", error)
        return []
    }
}

export async function revokeBlobUrl(src: string) {
    try {
        Logger.info("释放内存：" + src)
        const gifCacheManager = await GifCacheManager.getInstance();
        await gifCacheManager.revokeBlobUrl(src);
    } catch (error) {
        Logger.error("释放内存帧数据异常：", error)
        return []
    }
}