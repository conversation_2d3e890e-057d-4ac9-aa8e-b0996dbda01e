/**
 * GIF Worker工具类 - 在render包内部使用gif-worker
 */
import Logger from '../../logger'
import { workerManager, type GifFrameData } from '../../base/workers/worker-manager'

export class GifWorkerUtils {
    private static instance: GifWorkerUtils | null = null

    private constructor() {}

    /**
     * 获取GifWorkerUtils单例
     */
    public static getInstance(): GifWorkerUtils {
        if (!GifWorkerUtils.instance) {
            GifWorkerUtils.instance = new GifWorkerUtils()
        }
        return GifWorkerUtils.instance
    }

    /**
     * 使用worker加载GIF数据
     * @param url GIF文件URL
     * @param workerUrl 可选的worker文件URL
     * @returns 解析后的GIF帧数据
     */
    public async loadGifWithWorker(url: string, workerUrl?: string): Promise<GifFrameData> {
        try {
            // 如果提供了自定义worker URL，先初始化
            if (workerUrl) {
                await workerManager.initializeWorker('gif', workerUrl)
            }

            Logger.info('开始使用worker加载GIF:', url)
            const result = await workerManager.loadGifData(url)
            Logger.info('worker加载GIF完成，帧数:', result.frames.length)
            
            return result
        } catch (error) {
            Logger.error('worker加载GIF失败:', error)
            throw error
        }
    }

    /**
     * 将GIF帧数据转换为Blob URL数组
     * @param gifData GIF帧数据
     * @returns Blob URL数组
     */
    public convertFramesToBlobUrls(gifData: GifFrameData): string[] {
        try {
            const blobUrls: string[] = []
            
            for (const frame of gifData.frames) {
                const blobUrl = URL.createObjectURL(frame.frameBlob)
                blobUrls.push(blobUrl)
            }
            
            Logger.info('转换GIF帧为Blob URLs完成，数量:', blobUrls.length)
            return blobUrls
        } catch (error) {
            Logger.error('转换GIF帧为Blob URLs失败:', error)
            throw error
        }
    }

    /**
     * 释放Blob URL数组
     * @param blobUrls 要释放的Blob URL数组
     */
    public revokeBlobUrls(blobUrls: string[]): void {
        try {
            for (const blobUrl of blobUrls) {
                URL.revokeObjectURL(blobUrl)
            }
            Logger.info('释放Blob URLs完成，数量:', blobUrls.length)
        } catch (error) {
            Logger.error('释放Blob URLs失败:', error)
        }
    }

    /**
     * 完整的GIF处理流程：加载 -> 转换为Blob URLs
     * @param url GIF文件URL
     * @param workerUrl 可选的worker文件URL
     * @returns Blob URL数组和原始数据
     */
    public async processGif(url: string, workerUrl?: string): Promise<{
        blobUrls: string[]
        gifData: GifFrameData
    }> {
        try {
            // 1. 使用worker加载GIF
            const gifData = await this.loadGifWithWorker(url, workerUrl)
            
            // 2. 转换为Blob URLs
            const blobUrls = this.convertFramesToBlobUrls(gifData)
            
            return {
                blobUrls,
                gifData
            }
        } catch (error) {
            Logger.error('处理GIF失败:', error)
            throw error
        }
    }

    /**
     * 获取GIF基本信息（不加载完整数据）
     * @param gifData GIF数据
     * @returns GIF基本信息
     */
    public getGifInfo(gifData: GifFrameData): {
        frameCount: number
        dimensions: { width: number; height: number }
        totalDuration: number
        averageDelay: number
    } {
        const frameCount = gifData.frames.length
        const dimensions = gifData.dims
        const totalDuration = gifData.frames.reduce((sum, frame) => sum + frame.delay, 0)
        const averageDelay = frameCount > 0 ? totalDuration / frameCount : 0

        return {
            frameCount,
            dimensions,
            totalDuration,
            averageDelay
        }
    }

    /**
     * 预加载GIF（仅解析，不转换为Blob）
     * @param url GIF文件URL
     * @param workerUrl 可选的worker文件URL
     * @returns GIF基本信息
     */
    public async preloadGif(url: string, workerUrl?: string): Promise<{
        frameCount: number
        dimensions: { width: number; height: number }
        totalDuration: number
        averageDelay: number
    }> {
        try {
            const gifData = await this.loadGifWithWorker(url, workerUrl)
            return this.getGifInfo(gifData)
        } catch (error) {
            Logger.error('预加载GIF失败:', error)
            throw error
        }
    }

    /**
     * 销毁GIF worker
     */
    public async destroyGifWorker(): Promise<void> {
        try {
            await workerManager.destroyWorker('gif')
            Logger.info('GIF Worker已销毁')
        } catch (error) {
            Logger.error('销毁GIF Worker失败:', error)
        }
    }

    /**
     * 检查GIF worker是否已初始化
     */
    public isGifWorkerReady(): boolean {
        return workerManager.isWorkerInitialized('gif')
    }
}

// 导出单例实例
export const gifWorkerUtils = GifWorkerUtils.getInstance()

// 导出便捷函数
export async function loadGifWithWorker(url: string, workerUrl?: string): Promise<GifFrameData> {
    return gifWorkerUtils.loadGifWithWorker(url, workerUrl)
}

export async function processGifWithWorker(url: string, workerUrl?: string): Promise<{
    blobUrls: string[]
    gifData: GifFrameData
}> {
    return gifWorkerUtils.processGif(url, workerUrl)
}

export function revokeBlobUrls(blobUrls: string[]): void {
    return gifWorkerUtils.revokeBlobUrls(blobUrls)
}
