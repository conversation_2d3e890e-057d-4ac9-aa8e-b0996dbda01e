import { Canvas, FabricObject, Group, util } from "fabric";

/**
 * 获取元素的变换矩阵
 * @param element 元素
 * @returns 变换矩阵
 */
export const transformMatrix = (element: FabricObject) => {
  const matrix = element.calcTransformMatrix();
  const { flipX, flipY } = element.toObject()
  // const scaleX = Math.sqrt(matrix[0] * matrix[0] + matrix[1] * matrix[1]);
  // const scaleY = Math.sqrt(matrix[2] * matrix[2] + matrix[3] * matrix[3]);
  const { scaleX, scaleY } = util.qrDecompose(matrix)
  return {
    scaleX,
    scaleY,
    flipX,
    flipY,
  };
}
/**
 * 获取元素的相对位置
 * @param parent 父元素
 * @param element 元素
 * @returns 相对位置
 */
export const getRelativelyPosition =
(parent: Group | Canvas, element: FabricObject | Group): {
  relativelyLeft: number;
  relativelyTop: number;
} => {
  const isCanvas = parent instanceof Canvas
  if (isCanvas) {
    return {
      relativelyLeft: element.getCenterPoint().x,
      relativelyTop: element.getCenterPoint().y,
    }
  } else {
    const { translateX, translateY } = util.qrDecompose(element.calcOwnMatrix())
    return {
      relativelyLeft: translateX,
      relativelyTop: translateY,
    }
  }
}
