export enum CoreMouseMode {
  SELECTION = 'selection',
  FRAME = 'frame',
  TEXT = 'text',
}

export enum ObjectType {
  GROUP = 'group',
  FRAME = 'frame',
  TEXT = 'text',
  ACTIVE_SELECTION = 'activeselection',
}

export enum ElementName {
  FRAME = 'frame',
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  DYNAMIC_PICTURE = 'dynamicPicture',
  CONTAINER = 'container',
  LOADING_GROUP = 'loadingGroup',
  LOADING_RECT = 'loadingRect',
  LOADING_TEXT = 'loadingText',
  ERROR = 'error',
  MASK_GROUP = 'maskGroup',
  MASK_PATH = 'maskPath',
  MASK_RECT = 'maskRect',
  MASK_POLYGON = 'maskPolygon',
  MASK_CUSTOM = 'maskCustom',
  MASK_SMART = 'maskSmart',
}
/**
 * 对齐模式
 */
export enum AlignMode {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
  TOP = 'top',
  BOTTOM = 'bottom',
  MIDDLE = 'middle',
}

/**
 * 画笔类型
 */
export enum BrushType {
  FREE = 'free',
  ERASER = 'eraser',
}

/**
 * 警告类型
 */
export enum WarningType {
  IMAGE_COUNT = 1, // 图片数量超过限制
  TASK_CANCEL = 2, // 任务取消
  MASK_MAX_COUNT = 3, // mask数量超过限制
}

/**
 * loading类型
 */
export enum LoadingType {
  FADE_IN_TO_OUT = 'fadeInToOut',
  CIRCLE_DANCE = 'circleDance',
}
