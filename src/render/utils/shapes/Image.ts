import {Canvas, FabricImage, FabricObject, Group} from "fabric";
import { differenceWith, isEqual, omit, cloneDeep } from "lodash-es";

import {
    getImageCustomOptions,
    getImageOptions,
    ImageOptions,
    MaskPathOptions,
    MaskPolygonOptions,
    MaskRectOptions,
    ImageCustomOptions,
    Render,
    PathBrush,
    IImage,
    ElementName
} from "../../index";
import Logger from "../../logger";
import { createMask, getMaskOptions } from "./mask";
import { MASK_CONTAINER_NAME } from "../../base/package/brush/utils/getMouseDownTarget";

interface CalcPositionOptions {
    originOptions: ImageOptions & ImageCustomOptions;
    NewWidth: number;
    NewHeight: number;
    origin: 'lt' | 'rt' | 'lb' | 'rb' | 'center';
}

const calcPosition = ({
    originOptions,
    NewWidth,
    NewHeight,
    origin,
}: CalcPositionOptions) => {
    let position = {
        left: 0,
        top: 0,
    };
    switch (origin) {
        case 'lt':
            position = {
                left: originOptions._parent_id_
                    ? originOptions.relativelyLeft -
                    originOptions.width / 2 +
                    NewWidth / 2
                    : originOptions.left - originOptions.width / 2 + NewWidth / 2,
                top: originOptions._parent_id_
                    ? originOptions.relativelyTop -
                    originOptions.height / 2 +
                    NewHeight / 2
                    : originOptions.top - originOptions.height / 2 + NewHeight / 2,
            };
            break;
        case 'rt':
            position = {
                left: originOptions._parent_id_
                    ? originOptions.relativelyLeft +
                    originOptions.width / 2 -
                    NewWidth / 2
                    : originOptions.left + originOptions.width / 2 - NewWidth / 2,
                top: originOptions._parent_id_
                    ? originOptions.relativelyTop -
                    originOptions.height / 2 +
                    NewHeight / 2
                    : originOptions.top - originOptions.height / 2 + NewHeight / 2,
            };
            break;
        case 'lb':
            position = {
                left: originOptions._parent_id_
                    ? originOptions.relativelyLeft -
                    originOptions.width / 2 +
                    NewWidth / 2
                    : originOptions.left - originOptions.width / 2 + NewWidth / 2,
                top: originOptions._parent_id_
                    ? originOptions.relativelyTop +
                    originOptions.height / 2 -
                    NewHeight / 2
                    : originOptions.top + originOptions.height / 2 - NewHeight / 2,
            };
            break;
        case 'rb':
            position = {
                left: originOptions._parent_id_
                    ? originOptions.relativelyLeft +
                    originOptions.width / 2 -
                    NewWidth / 2
                    : originOptions.left + originOptions.width / 2 - NewWidth / 2,
                top: originOptions._parent_id_
                    ? originOptions.relativelyTop +
                    originOptions.height / 2 -
                    NewHeight / 2
                    : originOptions.top + originOptions.height / 2 - NewHeight / 2,
            };
            break;
        default:
            position = {
                left: originOptions._parent_id_
                    ? originOptions.relativelyLeft
                    : originOptions.left,
                top: originOptions._parent_id_
                    ? originOptions.relativelyTop
                    : originOptions.top,
            };
    }
    return position;
};

/**
 * 替换图片
 * @param element 元素
 * @param options 图片参数
 * @param origin 原点 默认center 可选值: lt, rt, lb, rb, center
 * @returns 图片
 */
export async function replaceImage(
    this: Render,
    element: Group,
    options: Partial<ImageCustomOptions & ImageOptions>,
    origin: 'lt' | 'rt' | 'lb' | 'rb' | 'center' = 'center',
    signal?: AbortSignal
) {
    const originOptions = {
        ...getImageOptions.call(
            this,
            (element as Group)
                .getObjects()
                .find((obj) => obj.type === 'image') as FabricImage
        ),
        ...getImageCustomOptions.call(this, element as IImage, false),
    };

    // 检查参数是否有变化
    const hasChanges = Object.keys(options).some(key => {
        if (key === '_mask_options_') {
            if (!options._mask_options_ || !originOptions._mask_options_) {
                return options._mask_options_ !== originOptions._mask_options_;
            }
            return !isEqual(options._mask_options_, originOptions._mask_options_);
        }
        return !isEqual((options as any)[key], (originOptions as any)[key]);
    });

    if (!hasChanges) {
        Logger.info('replaceImage: no changes detected, skipping replacement');
        return;
    }
    let position = {
        left: originOptions.left,
        top: originOptions.top,
    };
    if (options?.width && options?.height) {
        position = calcPosition({
            originOptions,
            NewWidth: options.width,
            NewHeight: options.height,
            origin,
        });
    }
    if (('_parent_id_' in options) && (options._parent_id_?? '') !== originOptions._parent_id_) {
        const targetParent = options._parent_id_ ? this.Finder.findById(options._parent_id_) as Group | Canvas : this._FC;
        (element?.parent ?? this._FC).remove(element)
        element.set({
            left: position.left,
            top: position.top,
        })
        targetParent.add(element)
        element.set({
            _parent_id_: options._parent_id_
        })

    } else {
        options.left = options?.relativelyLeft ?? position.left
        options.top = options?.relativelyTop ?? position.top
    }
    const image = element
        .getObjects()
        .find((obj) => obj.type === 'image') as FabricImage;

    if (options.src && options.src !== originOptions.src) {
        await image.setSrc(options.src, { signal, crossOrigin: 'anonymous' });
        image.set('dirty', true);
        Logger.info(`replaceImage src: ${options.src}`);
    }

    if (signal?.aborted) {
        Logger.info(`replaceImage signal aborted`);
        return;
    }

    if (options.width && options.height) {
        const scale = Math.max(
            options.width / image.width,
            options.height / image.height
        );
        image.scale(scale);
        image.setCoords();
        element.setCoords();
    }
    if (!options._mask_options_) {
        element.getObjects().forEach((item: FabricObject) => {
            if (item.get('isMaskContainer')) {
                element.remove(item)
            }
        })
    }
    options._mask_options_?.forEach(maskOption => {
        changeImageMask.call(element, maskOption._id_, maskOption._container_name_, maskOption._mask_options_)
    })

    element.set({
        ...options,
        width: options.width ?? originOptions.width,
        height: options.height ?? originOptions.height,
    });
    if (options.flipX !== void 0) {
        element.set('flipX', options.flipX)
    }
    if (options.flipY !== void 0) {
        element.set('flipY', options.flipY)
    }
    element.setCoords();
    image.set('dirty', true);
    this._FC.renderAll();
    if(this.Actions.getLoadingElsByIds().includes(element._id_)) {
        const loadingOptions = cloneDeep(this.Actions._loadingEls.get(element._id_))
        if(loadingOptions) {
            this.Actions.setLoaded(element._id_)
            this.Actions.setLoading({
                id: element._id_,
                text: loadingOptions.text,
                type: loadingOptions.type,
                blur: loadingOptions.blur,
            })
        }
    }
}

/**
 * 检测图片遮罩是否存在删除或新增
 */
function changeImageMask(
    this: Group,
    _id_: string,
    _container_name_: string,
    maskOptions: (MaskRectOptions | MaskPolygonOptions | MaskPathOptions)[]
) {
    const ignoreKeys = ['left', 'top'];
    const maskContainer = this.getObjects().find(item =>
         item.get('isMaskContainer') &&
         item._id_ === _id_ &&
         item.get('maskContainerName') === _container_name_
        ) as Group | undefined
    if (!maskContainer) return
    const currentOptions = getMaskOptions.call(this)
                            .find(item =>
                                item._id_ === _id_ &&
                                item._container_name_ === _container_name_
                            )
                            ?._mask_options_
    if (!currentOptions) return
    const cleanA = currentOptions.map(item => omit(item, ignoreKeys));
    const cleanB = maskOptions.map(item => omit(item, ignoreKeys));
    const deleted = differenceWith(cleanA, cleanB, isEqual);
    const added = differenceWith(cleanB, cleanA, isEqual);
    if (deleted.length > 0) {
        deleted.forEach(item => {
            const deleteTarget = maskContainer.getObjects().find(obj => obj._id_ === item._id_)
            if (deleteTarget) {
                const tipsContainer = maskContainer.parent?.getObjects().find(obj => obj.get('maskContainerName') === MASK_CONTAINER_NAME) as Group | undefined
                const tips = tipsContainer?.getObjects().find(obj => obj.get('removeTarget') === item._id_) as Group | undefined
                switch(deleteTarget._name_) {
                    case ElementName.MASK_RECT:
                        maskContainer.remove(deleteTarget)
                        this.canvas?.fire('mask:rect:deleted', { target: deleteTarget , element: this})
                        break;
                    case ElementName.MASK_POLYGON:
                        maskContainer.remove(deleteTarget)
                        this.canvas?.fire('mask:lasso:deleted', { target: deleteTarget , element: this})
                        break;
                    case ElementName.MASK_PATH:
                        maskContainer.remove(deleteTarget)
                        this.canvas?.fire('mask:path:deleted', { target: deleteTarget , element: this})
                        break;
                    case ElementName.MASK_SMART:
                        maskContainer.remove(deleteTarget)
                        this.canvas?.fire('mask:smart:deleted', { target: deleteTarget , element: this})
                        break;
                }
                if (tips) {
                    tipsContainer?.remove(tips)
                }
            }
        })
    }
    if (added.length > 0) {
        added.forEach(item => {
            createMask.call(maskContainer, item as MaskRectOptions | MaskPolygonOptions | MaskPathOptions)
        })
        const isVisible = maskContainer.visible
        if (this.canvas) {
            const iBaseBrush = new PathBrush(this.canvas, {
                width: 1,
                shapeColor: '#000',
                shapeContainerName: maskContainer.get('maskContainerName'),
                shapeContainerOpacity: maskContainer.opacity,
                showShapeTitle: added[0].showTitle || false,
                showShapeCloseBtn: added[0].showCloseBtn || false,
                targetElement: maskContainer.parent as IImage,
                erase: false,
                eraseShapeColor: '#000',
                eraseShapeOpacity: 0.3,
                isMask: false,
                isProtected: true,
            })
            iBaseBrush.renderTip()
            if (!isVisible) {
                iBaseBrush.destroy()
            }
        }
    }

}
