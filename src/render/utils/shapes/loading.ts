import { Canvas } from "fabric";
import { LoadingType } from "../enum";
import { CircleDanceLoader } from "../../base/package/animate/loaderByCenter";
import { Loader } from "../../base/package/animate/loader";
import { LoadingRectProperties } from "../../base/ownDefaults";
import { nanoid } from "nanoid";
import { LoadingOptions } from "../types";
export function createLoading(render: Canvas, opt: Omit<LoadingOptions, 'loadingTarget'>): LoadingOptions {
    const { type, el, blur, maskOpacity } = opt;
    let loadingTarget: Loader| CircleDanceLoader | null = null;
    switch (type) {
        case LoadingType.FADE_IN_TO_OUT:
            loadingTarget = new Loader({
                el,
                ...LoadingRectProperties,
                _id_: nanoid(),
                circleColor: 'white',
                circleRadius: 10,
                circleCount: 3,
                spacing: 5,
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                width: el.getScaledWidth() * ( el.parent?.scaleX ?? 1),
                height: el.getScaledHeight() * (el.parent?.scaleY ?? 1),
                evented: true,
                selectable: false,
              })
            break;
        case LoadingType.CIRCLE_DANCE:
            loadingTarget = new CircleDanceLoader({
                el,
                ...LoadingRectProperties,
                _id_: nanoid(),
                circleColor: 'white',
                circleRadius: 10,
                circleCount: 3,
                spacing: 5,
                blur,
                maskOpacity,
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                width: el.getScaledWidth() * ( el.parent?.scaleX ?? 1),
                height: el.getScaledHeight() * (el.parent?.scaleY ?? 1),
                evented: true,
                selectable: false,
              })
            break;
    }
    if (loadingTarget) {
        el.add(loadingTarget);
        loadingTarget.set({
            _parent_: el,
            left: 0,
            top: 0,
            angle: 0,
          });
        loadingTarget.initialize();
        render.renderAll();
    }
    return {
        loadingTarget,
        ...opt,
    };
}
