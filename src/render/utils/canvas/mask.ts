/**
 * 对mask图进行二值化处理
 * @param source 输入的ImageData或Blob
 * @param threshold 二值化阈值 (0-255)
 * @param format 输出格式 ('png' | 'jpeg' | 'webp')
 * @param quality 图片质量 (0-1，仅对jpeg有效)
 * @returns Promise<Blob> 处理后的图片Blob
 */
export async function binarizeMask(
    source: ImageData | Blob,
    threshold: number = 128,
    format: 'png' | 'jpeg' | 'webp' = 'png',
    quality: number = 1
): Promise<Blob> {
    let imageData: ImageData;

    // 如果输入是Blob，先转换为ImageData
    if (source instanceof Blob) {
        const img = await createImageFromBlob(source);
        imageData = imageToImageData(img);
    } else {
        imageData = source;
    }

    const data = imageData.data;
    const binaryData = new Uint8ClampedArray(data.length);

    for (let i = 0; i < data.length; i += 4) {
        // 计算灰度值 (使用加权平均)
        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;

        // 二值化处理
        const binaryValue = gray >= threshold ? 255 : 0;

        binaryData[i] = binaryValue;     // R
        binaryData[i + 1] = binaryValue; // G
        binaryData[i + 2] = binaryValue; // B
        binaryData[i + 3] = data[i + 3]; // A (保持原始透明度)
    }

    const processedImageData = new ImageData(binaryData, imageData.width, imageData.height);

    // 将处理后的ImageData转换为Blob
    return imageDataToBlob(processedImageData, format, quality);
}

/**
 * 从Blob创建Image对象
 * @param blob 图像Blob
 * @returns Promise<HTMLImageElement>
 */
function createImageFromBlob(blob: Blob): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        const url = URL.createObjectURL(blob);

        img.onload = () => {
            URL.revokeObjectURL(url);
            resolve(img);
        };

        img.onerror = () => {
            URL.revokeObjectURL(url);
            reject(new Error('Failed to load image from blob'));
        };

        img.src = url;
    });
}

/**
 * 将Image对象转换为ImageData
 * @param img Image对象
 * @returns ImageData
 */
function imageToImageData(img: HTMLImageElement): ImageData {
    const canvas = document.createElement('canvas');
    canvas.width = img.width;
    canvas.height = img.height;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
        throw new Error('Failed to get 2D context');
    }

    ctx.drawImage(img, 0, 0);
    return ctx.getImageData(0, 0, img.width, img.height);
}

/**
 * 将ImageData转换为Blob
 * @param imageData ImageData对象
 * @param format 输出格式
 * @param quality 图片质量
 * @returns Promise<Blob>
 */
function imageDataToBlob(
    imageData: ImageData,
    format: 'png' | 'jpeg' | 'webp' = 'png',
    quality: number = 1
): Promise<Blob> {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        canvas.width = imageData.width;
        canvas.height = imageData.height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
            reject(new Error('Failed to get 2D context'));
            return;
        }

        ctx.putImageData(imageData, 0, 0);

        canvas.toBlob((blob) => {
            if (blob) {
                resolve(blob);
            } else {
                reject(new Error('Failed to convert canvas to blob'));
            }
        }, `image/${format}`, quality);
    });
}
