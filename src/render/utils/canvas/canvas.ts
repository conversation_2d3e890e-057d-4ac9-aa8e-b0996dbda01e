import {
  Canvas,
  Point,
  FabricObject,
  TPointerEvent,
  TMat2D,
  ActiveSelection,
  Group,
} from 'fabric';
import { findCustomGroupById, isRealElement } from '../shapes';
import { Render } from '../../render';
import { ElementName } from '../enum';
import { IImage } from '../../base/package/image/image';
import { defaultMaxZoom, defaultMinZoom } from '../../base/ownDefaults';
import { Area, ElementOptions } from '../types';
import Logger from '../../logger';
/**
 * 将画布中当前选中的元素居中显示在屏幕中间
 * @param {Canvas} canvas - 画布实例
 * @param {FabricObject} activeObject - 当前选中的元素
 * @param {number} duration - 动画持续时间(ms)
 */
export function centerSelectedObject(
  canvas: Canvas,
  activeObject: FabricObject,
  duration = 500,
  scaleFactor: number  // 对象应该占据视口的比例
) {
  if (!canvas.viewportTransform) return;
  if (!activeObject) return;

  if (!activeObject.width || !activeObject.height) return;

  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  const canvasCenter = new Point(canvasWidth / 2, canvasHeight / 2);

  // 获取当前缩放比例
  let targetZoom = canvas.getZoom();
  // 检查对象是否在视口外
  if (isObjectOutOfViewport(canvas, activeObject)) {
    // 计算使对象适合视口的缩放比例

    const widthRatio =
      (canvasWidth * scaleFactor) / activeObject.getScaledWidth();
    const heightRatio =
      (canvasHeight * scaleFactor) / activeObject.getScaledHeight();

    // 选择较小的比例，确保对象完全在视口内
    targetZoom = Math.min(widthRatio, heightRatio);

    // 设置最大缩放限制
    const maxZoom = 20;
    targetZoom = Math.min(targetZoom, maxZoom);

    // 确保不低于最小缩放限制
    targetZoom = Math.max(targetZoom, defaultMinZoom);
  } else {
    Logger.info('Object is within viewport, no scaling applied');
  }

  // 获取对象中心点
  const objectCenter = activeObject.getCenterPoint();
  if (!objectCenter) return;

  const viewportTransform = canvas.viewportTransform;
  const currentPan = {
    x: viewportTransform[4],
    y: viewportTransform[5],
  };
  const currentZoom = canvas.getZoom();

  // 计算目标平移位置，使对象居中
  const targetPanX = canvasCenter.x - objectCenter.x * targetZoom;
  const targetPanY = canvasCenter.y - objectCenter.y * targetZoom;

  const startTime = Date.now();

  const animate = () => {
    const now = Date.now();
    const progress = Math.min(1, (now - startTime) / duration);

    // 使用缓动函数使动画更自然
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    // 计算当前帧的平移和缩放值
    const currentX = currentPan.x + (targetPanX - currentPan.x) * easeProgress;
    const currentY = currentPan.y + (targetPanY - currentPan.y) * easeProgress;
    const currentScaling = Math.max(
      currentZoom + (targetZoom - currentZoom) * easeProgress, // 修复：移除了0.8倍率限制
      defaultMinZoom
    );

    if (
      !isFinite(currentX) ||
      !isFinite(currentY) ||
      !isFinite(currentScaling)
    ) {
      Logger.error('Invalid transform values detected');
      return;
    }

    // 创建新的视口变换矩阵
    const newViewportTransform: TMat2D = [
      currentScaling,
      viewportTransform[1],
      viewportTransform[2],
      currentScaling,
      currentX,
      currentY,
    ];

    try {
      // 应用新的视口变换
      canvas.setViewportTransform(newViewportTransform);
      canvas.requestRenderAll();
      canvas.fire('viewport:translate', {
        delta: { x: currentX, y: currentY },
        left: currentX,
        top: currentY,
      });
      canvas.fire('viewport:zoom', { zoom: currentScaling });
    } catch (e) {
      Logger.error('Error applying viewport transform:', e);
      return;
    }

    // 继续动画或完成
    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // 动画完成后检查对象是否可见
      setTimeout(() => {
        if (isObjectOutOfViewport(canvas, activeObject)) {
          Logger.warn('Object still not fully visible after animation');
        }
      }, 100);
    }
  };

  // 开始动画
  animate();
}

/**
 * 检测元素是否超出视口的80%
 * @param {Canvas} canvas - 画布实例
 * @param {FabricObject} object - 元素
 * @returns {boolean} - 是否超出视口的80%
 */
export function isObjectOutOfViewport(
  canvas: Canvas,
  obj: FabricObject
): boolean {
  // Ensure canvas and viewportTransform exist
  if (!canvas || !canvas.viewportTransform) return false;

  const viewportTransform = canvas.viewportTransform;
  const zoom = canvas.getZoom();

  // Calculate visible area considering zoom and pan
  const visibleAreaCanvas = {
    left: -viewportTransform[4] / zoom,
    top: -viewportTransform[5] / zoom,
    right: (canvas.width - viewportTransform[4]) / zoom,
    bottom: (canvas.height - viewportTransform[5]) / zoom,
  };

  const coords = obj.getCoords();
  const objBounds = {
    left: Math.min(...coords.map((coord) => coord.x)),
    top: Math.min(...coords.map((coord) => coord.y)),
    right: Math.max(...coords.map((coord) => coord.x)),
    bottom: Math.max(...coords.map((coord) => coord.y)),
  };

  if (
    objBounds.right > visibleAreaCanvas.right ||
    objBounds.left < visibleAreaCanvas.left ||
    objBounds.bottom > visibleAreaCanvas.bottom ||
    objBounds.top < visibleAreaCanvas.top
  ) {
    return true;
  }

  return false;
}

/**
 * 判断是否已知类型元素
 * @param element 元素
 * @returns 是否已知类型元素
 */
export function isKnownTypeElement(element: FabricObject) {
  return (
    element._name_ === ElementName.IMAGE ||
    element._name_ === ElementName.TEXT ||
    element._name_ === ElementName.FRAME ||
    element._name_ === ElementName.CONTAINER ||
    element._name_ === ElementName.VIDEO ||
    element._name_ === ElementName.DYNAMIC_PICTURE
  );
}

/**
 * 将画布中所有元素展示在可视化区域内
 * @param {Canvas} canvas - 画布实例
 * @param {FabricObject[]} targets - 需要居中的元素（不传或长度为0时为画布中所有元素）
 * @param {number} duration - 动画持续时间(ms)
 * @param {number} limitScale - 限制最小缩放比例
 * @param {number} leftPanelWidth - 左侧面板宽度
 * @param {number} rightPanelWidth - 右侧面板宽度
 * @param {number} topPanelHeight - 上侧面板高度
 * @param {number} bottomPanelHeight - 下侧面板高度
 */
export function showAllObjects(
  canvas: Canvas,
  targets: Array<FabricObject> = [],
  limitScale = defaultMinZoom,
  leftPanelWidth = 0,
  rightPanelWidth = 0,
  topPanelHeight = 0,
  bottomPanelHeight = 0,
  duration = 500,
  padding = 40
) {
  const minZoom = canvas.get('_minZoom') || defaultMinZoom;
  const maxZoom = canvas.get('_maxZoom') || defaultMaxZoom;
  limitScale = Math.max(Math.min(maxZoom, limitScale), minZoom);
  const objects = targets.length ? targets : canvas.getObjects().filter(isKnownTypeElement);
  if (!objects.length || !canvas.viewportTransform) {
    canvas.setZoom(1);
    canvas.fire('viewport:zoom', { zoom: 1 });
    return;
  }

  // 计算所有对象的边界
  let minLeft = Infinity,
    minTop = Infinity,
    maxRight = -Infinity,
    maxBottom = -Infinity;

  objects.forEach((obj) => {
    const objBoundingBox = obj.getBoundingRect();
    minLeft = Math.min(minLeft, objBoundingBox.left);
    minTop = Math.min(minTop, objBoundingBox.top);
    maxRight = Math.max(maxRight, objBoundingBox.left + objBoundingBox.width);
    maxBottom = Math.max(maxBottom, objBoundingBox.top + objBoundingBox.height);
  });

  const width = maxRight - minLeft;
  const height = maxBottom - minTop;
  const center = {
    x: minLeft + width / 2,
    y: minTop + height / 2,
  };

  // 计算适当的缩放比例，添加一些内边距
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();
  // 减去两侧面板的宽度和内边距计算实际可用宽度
  const availableWidth =
    canvasWidth - padding * 2 - leftPanelWidth - rightPanelWidth;
  const scaleX = availableWidth / width;
  const scaleY = (canvasHeight - padding * 2 - topPanelHeight - bottomPanelHeight) / height;
  const targetZoom = Math.max(
    limitScale,
    Math.min(scaleX, scaleY, maxZoom)
  );
  // 获取当前状态
  const currentZoom = canvas.getZoom();
  const currentPan = {
    x: canvas.viewportTransform[4],
    y: canvas.viewportTransform[5],
  };

  // 计算目标平移位置，使内容居中
  const targetPan = {
    // 考虑左侧面板宽度对中心点的影响
    x:
      (canvasWidth + leftPanelWidth - rightPanelWidth) / 2 -
      center.x * targetZoom,
    // y方向需要底部留出bottomPanelHeight的空间
    y: (canvasHeight + topPanelHeight - bottomPanelHeight) / 2 - center.y * targetZoom,
  };

  // 动画过渡
  const startTime = Date.now();
  const animate = () => {
    const now = Date.now();
    const progress = Math.min(1, (now - startTime) / duration);

    // 使用easeInOutCubic缓动函数使动画更平滑
    const eased =
      progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

    const currentX = currentPan.x + (targetPan.x - currentPan.x) * eased;
    const currentY = currentPan.y + (targetPan.y - currentPan.y) * eased;
    const currentScaling = currentZoom + (targetZoom - currentZoom) * eased;
    const newViewportTransform: TMat2D = [
      currentScaling,
      0,
      0,
      currentScaling,
      currentX,
      currentY,
    ];
    canvas.setViewportTransform(newViewportTransform);
    canvas.requestRenderAll();
    canvas.fire('viewport:translate', { delta: { x: currentX, y: currentY }, left: currentX, top: currentY });
    canvas.fire('viewport:zoom', { zoom: currentScaling });

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  animate();
}

/**
 * 获取视口内的所有元素
 * @param {Canvas} canvas - 画布实例
 * @returns {Array<FabricObject>} - 视口内的所有元素
 */
export function getVisibleObjects(canvas: Canvas): Array<FabricObject> {
  const viewportTransform = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];
  const zoom = canvas.getZoom();
  const visibleArea = {
    left: -viewportTransform[4] / zoom,
    top: -viewportTransform[5] / zoom,
    right: (canvas.width - viewportTransform[4]) / zoom,
    bottom: (canvas.height - viewportTransform[5]) / zoom,
  };
  return canvas.getObjects().filter(isRealElement).filter((obj) => {
    if (!obj?.get('visible')) return false;
    const c = obj.getCoords();
    const objBoundingBox = {
      left: Math.min(...c.map((item) => item.x)),
      top: Math.min(...c.map((item) => item.y)),
      right: Math.max(...c.map((item) => item.x)),
      bottom: Math.max(...c.map((item) => item.y)),
    };
    return (
      objBoundingBox.left < visibleArea.right &&
      objBoundingBox.top < visibleArea.bottom &&
      objBoundingBox.right > visibleArea.left &&
      objBoundingBox.bottom > visibleArea.top
    );
  });
}

/**
 * 获取视口位置
 * @param canvas - 画布实例
 * @returns {
 *  left: number;
 *  top: number;
 *  right: number;
 *  bottom: number;
 * } - 视口位置
 */
export function getViewPortVisibleArea(canvas: Canvas): {
  left: number;
  top: number;
  right: number;
  bottom: number;
  centerX: number;
  centerY: number;
} {
  const vpt = canvas.viewportTransform || [1, 0, 0, 1, 0, 0];

  const visibleArea = {
    left: -vpt[4] / vpt[0],
    top: -vpt[5] / vpt[3],
    right: (canvas.width - vpt[4]) / vpt[0],
    bottom: (canvas.height - vpt[5]) / vpt[3],
    centerX: (canvas.width / 2 - vpt[4]) / vpt[0],
    centerY: (canvas.height / 2 - vpt[5]) / vpt[3],
  };

  return visibleArea;
}

/**
 * 判断鼠标是否在对象上
 * @param {Canvas} canvas - 画布实例
 * @param {PointerEvent} event - 鼠标事件
 * @param {FabricObject} targetObject - 目标对象
 * @returns {boolean} - 是否在对象上
 */
export function isMouseOverObject(
  canvas: Canvas,
  event: TPointerEvent,
  targetObject: FabricObject
): boolean {
  const pointer = canvas.getScenePoint(event);
  const boundingBox = targetObject.getBoundingRect();
  return (
    pointer.x >= boundingBox.left &&
    pointer.x <= boundingBox.left + boundingBox.width &&
    pointer.y >= boundingBox.top &&
    pointer.y <= boundingBox.top + boundingBox.height
  );
}

/**
 * 元素Z轴排序
 * @param {FabricObject[]} elements - 元素数组
 */
export function sortElementsByZIndex(elements: Array<ElementOptions>) {
  elements.sort((a, b) => {
    const aIndex = a.zIndex || 0;
    const bIndex = b.zIndex || 0;
    return aIndex - bIndex;
  });

  for (const element of elements) {
    if ('children' in element && element.children && element.children.length > 0) {
      sortElementsByZIndex(element.children as ElementOptions[]);
    }
  }

  return elements;
}
  

/**
 * 元素的父元素是否是同一个
 * @param {FabricObject} element - 元素
 * @returns {boolean} - 父元素是否是同一个
 */
export function isSameParent(elements: Array<FabricObject>): boolean {
  const parents: Array<string> = [];
  elements.forEach((el) => {
    if (parents.includes(el._parent_id_)) return;
    parents.push(el._parent_id_);
  });
  return parents.length === 1;
}
/**
 * 选中元素
 * @param {Canvas} canvas - 画布实例
 * @param {FabricObject[]} elements - 元素数组
 */
export function selectionElements(canvas: Canvas, elements: FabricObject[]) {
  if (elements.length > 1) {
    const as = new ActiveSelection();
    as.add(...elements);
    canvas.setActiveObject(as);
  } else {
    canvas.setActiveObject(elements[0]);
  }
}

/**
 * 获取元素的zIndex
 * @param canvas - 画布实例
 * @param element - 元素
 * @returns {number} - 元素的zIndex
 */
export function getElementZIndex(
  canvas: Canvas | Group,
  element: FabricObject
): number {
  const zIndex = canvas
    .getObjects()
    .findIndex((obj) => obj._id_ === element._id_);
  if (zIndex !== -1) return zIndex;
  const parentId = element._parent_id_;
  const parentElement = findCustomGroupById(canvas, parentId);
  if (parentElement) {
    return getElementZIndex(parentElement as Group, element);
  }
  return 0;
}

/**
 * 判断点是否在绘制区域内
 * @param point 点
 * @param drawableObjects 绘制对象
 * @returns {boolean} - 是否在绘制区域内
 */
export const isInDrawableArea = (
  point: Point,
  drawableObject: FabricObject
) => {
  const objLeft = drawableObject.left;
  const objTop = drawableObject.top;
  const objRight = objLeft + drawableObject.width * drawableObject.scaleX;
  const objBottom = objTop + drawableObject.height * drawableObject.scaleY;

  return (
    point.x >= objLeft &&
    point.x <= objRight &&
    point.y >= objTop &&
    point.y <= objBottom
  );
};

/**
 * 获取所有图片元素
 * @param {Render} this - 渲染实例
 * @returns {Group[]} - 所有图片元素
 */
export function getAllImageElements(this: Render): IImage[] {
  const isGroup = (obj: FabricObject): obj is Group => obj?.type === 'group';

  const isImage = (obj: FabricObject) => obj?.type === 'iimage';
  const images: IImage[] = [];
  const getImageElByChild = (groups: Group[]) => {
    groups.forEach((group) => {
      const images = group.getObjects().filter(isImage);
      images.forEach((image) => {
        images.push(image);
      });
      getImageElByChild(group.getObjects().filter(isGroup));
    });
  };
  try {
    const groups = this._FC.getObjects().filter((item) => isGroup(item));
    const topImages = this._FC.getObjects().filter(isImage) as IImage[];
    getImageElByChild(groups);
    return [...images, ...topImages];
  } catch {
    return [];
  }
}

/**
 * 判断点是否在对象上
 * @param canvas - 画布实例
 * @param point - 点
 * @returns {FabricObject | Canvas} - 对象或画布
 */
export function scenePointInObject(
  canvas: Canvas,
  point: Point
): FabricObject | Canvas {
  const objects = canvas.getObjects().reverse();
  for (const obj of objects) {
    if (obj.containsPoint(point)) {
      return obj;
    }
  }
  return canvas;
}

/**
 * 判断对象是否在区域内
 * @param this - 画布实例
 * @param area - 区域
 * @returns {boolean} - 是否在区域内
 */
export function isObjectInArea(
    this: Canvas,
    area: Area,
    fullyContained: boolean = true
): boolean {
  const { x, y, width, height } = area;
  const elements = this.getObjects();

  for (const element of elements) {
    const elementBounds = element.getBoundingRect();

    if (fullyContained) {
      // 检查对象是否完全在区域内
      const isInside =
          elementBounds.left >= x &&
          elementBounds.top >= y &&
          elementBounds.left + elementBounds.width <= x + width &&
          elementBounds.top + elementBounds.height <= y + height;

      if (isInside) return true;
    } else {
      // 检查对象是否与区域有重叠
      const isOverlapping =
          elementBounds.left <= x + width &&
          elementBounds.left + elementBounds.width >= x &&
          elementBounds.top <= y + height &&
          elementBounds.top + elementBounds.height >= y;

      if (isOverlapping) return true;
    }
  }

  return false;
}

/**
 * 将形状转换为Blob
 * @param shape - 形状
 * @returns {Promise<Blob>} - Blob
 */
export function transformShapeToBlob(shape: FabricObject, ext: string = 'png'): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const canvas = shape.toCanvasElement()
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error('Failed to convert shape to blob'));
      }
    }, ext);
  });
}

/**
 * 判断两个矩形是否相交
 * @param rect1 矩形1
 * @param rect2 矩形2
 * @returns {boolean} - 是否相交
 */
export function isIntersectRect(rect1: { left: number, top: number, right: number, bottom: number }, rect2: { left: number, top: number, right: number, bottom: number }): boolean {
  return rect1.left < rect2.right && rect1.right > rect2.left && rect1.top < rect2.bottom && rect1.bottom > rect2.top;
}

export function isAreaIntersectByFabricObject(area: { left: number, top: number, right: number, bottom: number }, fabricObject: FabricObject) {
  const aCords = fabricObject.getCoords();
  const objBounds = {
    left: Math.min(...aCords.map((item) => item.x)),
    top: Math.min(...aCords.map((item) => item.y)),
    right: Math.max(...aCords.map((item) => item.x)),
    bottom: Math.max(...aCords.map((item) => item.y)),
  }
  return isIntersectRect(area, objBounds);
}

export function getObjectBounds(fabricObject: FabricObject) {
  const aCords = fabricObject.getCoords();
  return {
    left: Math.min(...aCords.map((item) => item.x)),
    top: Math.min(...aCords.map((item) => item.y)),
    right: Math.max(...aCords.map((item) => item.x)),
    bottom: Math.max(...aCords.map((item) => item.y)),
  }
}



