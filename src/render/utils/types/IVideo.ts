import { z } from "zod/v4";
import { BaseElementOptionsSchema, ElementSizeSchema } from "./basic";
import { MaskOptionsGroupSchema } from "./mask";

export const IVideoBaseOptionsSchema = BaseElementOptionsSchema.extend({
    cover: z.string(),
    source: z.string(),
    frameType: z.enum(['video', 'dynamicPicture']).optional().default('video'),
    videoWidth: z.number().positive(),
    videoHeight: z.number().positive(),
})

export const IVideoOptionsSchema = IVideoBaseOptionsSchema.extend({
    _mask_options_: z.array(MaskOptionsGroupSchema).optional().default([])
}).extend(ElementSizeSchema.shape)
