import { z } from "zod/v4";
import { BaseElementOptionsSchema, ElementSizeSchema } from "./basic";
import { MaskOptionsGroupSchema } from "./mask";

export const IDynamicPictureBaseOptionsSchema = BaseElementOptionsSchema.extend({
    cover: z.string(),
    src: z.string(),
    imageWidth: z.number().positive(),
    imageHeight: z.number().positive(),
})

export const IDynamicPictureOptionsSchema = IDynamicPictureBaseOptionsSchema.extend({
    _mask_options_: z.array(MaskOptionsGroupSchema).optional().default([])
}).extend(ElementSizeSchema.shape)
