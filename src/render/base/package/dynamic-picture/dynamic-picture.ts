import { classRegistry, FabricImage, FabricObject, Group, LayoutManager, util } from "fabric";
import { videoDefaultValues } from "./constant";
import { IDynamicPictureProps } from "./types";
import { layoutAfter } from "./utils";
import { _set } from "./_set";
import { FrameFixedLayout } from "../layout/frameFixed";
import { createIconImage } from "./render/constant";
import { dynamicPictureToFrame, revokeBlobUrl } from "../../../utils/sprite/gif";

// 导入重构后的模块
import { GifCacheManager } from "./utils/gif-cache-manager";
import Logger from "../../../logger";
import { cahceFrame } from "./utils/frames";

export class IDynamicPicture extends Group {
    static type = 'IDynamicPicture';
    declare clip: boolean;
    static ownDefaults = videoDefaultValues;
    static cacheProperties = [...Group.cacheProperties, 'clip'];
    declare sourceSrc: string;
    declare coverSrc: string;
    declare _shape_name_: string;
    declare IConImage: FabricImage;
    declare fabricImage: FabricImage
    public  loaded: boolean = false;

    private mainImage: FabricImage | null = null;
    private framesImage: FabricImage[] = [];
    public autoPlay: boolean = true;
    private isGifLoaded: boolean = false; // 标记GIF是否已加载完成
    private originalSrc: string | null = null; // 原始URL
    public isPlaying: boolean = false; // 当前是否允许播放
    private currentFrameIndex: number = 0; // 当前帧索引
    private frameDelay: number = 200 // 帧延迟
    private previousFrameTime: number = 0; // 上一帧渲染的时间

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...videoDefaultValues,
        };
    }

    constructor(options: Partial<IDynamicPictureProps & { autoPlay?: boolean }>) {
        const { cover, left, top, scaleX, scaleY, flipX, flipY, autoPlay = true, ...rest } = options;
        const opts = { ...IDynamicPicture.ownDefaults, ...rest };
        opts.layoutManager = opts.layoutManager || new LayoutManager(new FrameFixedLayout());

        if (!options.cover) {
            throw new Error('cover is required');
        }
        const image = new FabricImage(options.cover, { crossOrigin: 'anonymous' });
        const originalWidth = options.cover.width;
        const originalHeight = options.cover.height;
        const targetWidth = options.width || originalWidth;
        const targetHeight = options.height || originalHeight;

        image.set({
            width: originalWidth,
            height: originalHeight,
            selectable: false,
            dirty: true,
        });

        super([image], opts);

        // 设置自动播放选项
        this.autoPlay = autoPlay;
        this.setCoords();
        this.dirty = true;
        this.coverSrc = options.cover.src;
        this._shape_name_ = options._shape_name_ || '';
        this.mainImage = image; // 保存主图像引用
        this.fabricImage = image
        this.on("layout:after", layoutAfter.bind(this));
        const iconWidth = 44
        const iconHeight = 44
        createIconImage().then(iconImage => {
            iconImage.width = iconWidth
            iconImage.height = iconHeight
            const selfLeft = this.getScaledWidth() / 2 - iconWidth / 2;
            const selfTop = -this.getScaledHeight() / 2 + iconHeight / 2;
            this.IConImage = new FabricImage(iconImage, {
                left: selfLeft,
                top: selfTop,
                width: 44,
                height: 44,
                selectable: false,
                evented: false, // 允许事件以便点击控制播放
                hasControls: false,
                hasBorders: false,
            });
            this.add(this.IConImage)
            this.IConImage.set({
                left: selfLeft,
                top: selfTop,
            })

            this.on("scaling", this.updateIconStability);
            this.on("rotating", this.updateIconStability);
            this.on("moving", this.updateIconStability);
            this.on("modified", this.updateIconStability);
            this.on("added", this.updateIconStability);
            this.bindEvents()
            // 初始化一次
            this.updateIconStability();
            this.canvas?.renderAll()
        })
        this.parseDynamicFrames(options.cover.src ?? '')
        const scale = (options.width && options.height) ? Math.max(options.width / image.width, options.height / image.height) : 1;
        image.scale(scale);
        image.set({ _parent_: this });
        this._setProperties({ width: targetWidth, height: targetHeight, left, top, scaleX, scaleY });
    }
    bindEvents = () => {
        if (this.canvas) {
            this.canvas.on("viewport:translate", this.updateIconStability);
            this.canvas.on("viewport:zoom", this.updateIconStability);
        } else {
            setTimeout(this.bindEvents, 100); // 延迟检查
        }
    };

    public startGifAnimationFromBeginning = () => {
        if (!this.isGifLoaded) {
            Logger.info('GIF尚未加载完成，无法播放');
            return;
        }
        if (this.isPlaying) {
            Logger.info('GIF正在播放中，无需重复播放');
            return;
        }
        this.isPlaying = true
        this.startGifAnimation();
    };

    public stopGifAnimationAndRestoreCover = () => {
        Logger.info('已停止播放并恢复封面');
        this.stopGifAnimation();
    };
    _setProperties(properties: Record<string, any>) {
        for (const [key, value] of Object.entries(properties)) {
            this._set(key, value);
        }
    }

    updateIconStability = () => {
        const canvasZoom = this.canvas?.getZoom() || 1;
        const groupWidth = this.getScaledWidth() * canvasZoom
        const groupHeight = this.getScaledHeight() * canvasZoom
        this.IConImage?.set({
            scaleX: 1 / (this.scaleX * canvasZoom),
            scaleY: 1 / (this.scaleY * canvasZoom),
            left: this.flipX ? -this.width / 2 : this.width / 2,   // 右上角
            top: this.flipY ? this.height / 2 : -this.height / 2,
            originX: this.flipX ? "left" : "right",
            originY: this.flipY ? "bottom" : "top",
            flipX: this.flipX,
            flipY: this.flipY,
            visible: !(
                groupWidth < 44 || groupHeight < 44
            ),
        });
        this.canvas?.renderAll()
    };

    remove(...objects: FabricObject[]) {
        this.dirty = false;
        return super.remove(...objects);
    }

    render(ctx: CanvasRenderingContext2D) {
        super.render(ctx)
    }

    parseDynamicFrames = async (src: string) => {
        try {
            this.originalSrc = src;
            const frames = await dynamicPictureToFrame(src);
            if (frames && frames.length > 0) {
                Logger.info(`解析到 ${frames.length} 帧GIF动画`);
                this.framesImage.push(...await cahceFrame(frames))
                await revokeBlobUrl(src)
                this.isGifLoaded = true;
                this.add(...this.framesImage)
                this.framesImage.forEach(item => {
                    item.set("left", 0)
                    item.set("top", 0)
                    this.sendObjectBackwards(item)
                })
                if (this.isCurrentSelection) {
                    this.startGifAnimationFromBeginning()
                }
            } else {
                Logger.warn('未能解析到有效的GIF帧数据');
            }
        } catch (error) {
            Logger.error('解析GIF时出错:', error);
        }
        return frames || [];
    }

    public get isCurrentSelection ()  {
        if (!this.canvas) return false;
        const activeObject = this.canvas.getActiveObject();
        if (!activeObject) return false;
        if (activeObject === this) return true;
        const activeObjects = this.canvas.getActiveObjects();
        return activeObjects.includes(this);
    }

    private renderGifFrame = (time: DOMHighResTimeStamp) => {
            if (!this.isPlaying) {
                Logger.warn("播放状态未开启")
                this.mainImage?.set("visible", true)
                this.framesImage.forEach(item => {
                    item.set("visible", false)
                })
                this.forceRender()
                return
            }
            util.requestAnimFrame(this.startGifAnimation)
            if (time - this.previousFrameTime < this.frameDelay) {
                return
            }
            this.previousFrameTime = time
            const mainImageVisible = this.mainImage?.visible
            if (mainImageVisible) {
                this.mainImage?.set("visible", false)
            }
            const currentFrame = this.framesImage[this.currentFrameIndex]
            currentFrame.set("visible", true)
            const otherFrames = this.framesImage.filter(item => item !== currentFrame)
            otherFrames.forEach(item => {
                item.set("visible", false)
            })
            this.forceRender()
            this.currentFrameIndex++
            if (this.currentFrameIndex >= this.framesImage.length) {
                this.currentFrameIndex = 0
            }
        }

    // 开始播放GIF动画
    private startGifAnimation = () => {
        util.requestAnimFrame(this.renderGifFrame)
    }

    // 停止播放GIF动画
    private stopGifAnimation = () => {
        this.isPlaying = false
        this.framesImage.forEach(item => {
            item.set("visible", false)
        })
        this.mainImage?.set("visible", true)
        this.currentFrameIndex = 0
        this.forceRender()
    }

    // 强制触发渲染
    private forceRender = () => {
        try {
            // 多种渲染触发方式
            if (this.canvas) {
                this.canvas.renderAll();
                this.canvas.requestRenderAll();
            }

            // 如果在组中，触发组的渲染
            if (this.group && this.group.canvas) {
                this.group.canvas.renderAll();
                this.group.canvas.requestRenderAll();
            }

            // 触发对象自身的重绘
            this.setCoords();
        } catch (error) {
            Logger.error('强制渲染失败:', error);
        }
    }

    set(key: string | Record<string, any>, value?: any) {
        super.set(key, value);
        this.canvas?.fire('object:changed', { target: this });
        return this;
    }

    _set(key: string, value: any) {
        _set.call(this, key, value);
        return this;
    }

    toObject(propertiesToInclude: any = []): any {
        return {
            ...super.toObject(propertiesToInclude),
            width: this.width,
            height: this.height,
            clip: this.clip,
            sourceSrc: this.sourceSrc,
            coverSrc: this.coverSrc,
            autoPlay: this.autoPlay,
        };
    }

    dispose() {
        this.stopGifAnimation();
        this.canvas?.off("viewport:translate", this.updateIconStability);
        this.canvas?.off("viewport:zoom", this.updateIconStability);
        super.dispose();
    }

    // 预加载GIF到缓存
    public async preloadGif(src: string): Promise<void> {
        try {
            Logger.info(`GIF预加载完成: ${src}`);
        } catch (error) {
            Logger.error(`GIF预加载失败: ${src}`, error);
        }
    }

    // 清除指定GIF的缓存
    public async clearGifCache(src?: string): Promise<void> {
        try {
            const cacheManager = await GifCacheManager.getInstance();
            const targetSrc = src || this.originalSrc;
            if (targetSrc) {
                await cacheManager.clearCache(targetSrc);
                Logger.info(`清除GIF缓存: ${targetSrc}`);
            }
        } catch (error) {
            Logger.error('清除GIF缓存失败:', error);
        }
    }

    toString() {
        return `#<IDynamicPicture: (${this.complexity()})>`;
    }
}

classRegistry.setClass(IDynamicPicture);
