import { IDynamicPictureOptionsSchema } from "../../../utils/types"
import { GroupOwnProps, GroupProps } from "fabric";
import { z } from "zod/v4"

export type IDynamicPictureOptions = z.infer<typeof IDynamicPictureOptionsSchema>

export interface IDynamicPictureOwnProps extends GroupOwnProps, Omit<IDynamicPictureOptions, 'angle' | 'cover'> {
    clip: boolean;
    cover: HTMLImageElement;
}

export interface IDynamicPictureProps extends GroupProps, IDynamicPictureOwnProps {}