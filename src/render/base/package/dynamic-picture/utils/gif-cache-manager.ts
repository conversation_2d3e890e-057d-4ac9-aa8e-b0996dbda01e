/**
 * GIF缓存管理器
 * 负责GIF帧数据的IndexedDB存储和管理
 */
import { parseGIF, decompressFrames } from "gifuct-js"
import { composeFullFrames, fullFrameToBlob  } from "../../../../utils/sprite/parsedGif";
import { DynamicPictureDb } from '../../store/dynamic-picture/dynamic-picture';
import { CachedGifDataSchema } from '../../store/dynamic-picture/schema';
import { normalizeUrlForStorage, isValidHttpUrl, isBlobUrl } from './url-utils';
import { GifFrame } from '../types/gif-frame';
import Logger from '../../../../logger';
import { z } from 'zod';

type CachedGifData = z.infer<typeof CachedGifDataSchema>;

export class GifCacheManager {
    private static instance: GifCacheManager | null = null;
    private db: DynamicPictureDb | null = null;
    private isInitialized: boolean = false;

    private blobUrlManager: Map<string, string[]> = new Map();

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static async getInstance(): Promise<GifCacheManager> {
        if (!GifCacheManager.instance) {
            GifCacheManager.instance = new GifCacheManager();
            await GifCacheManager.instance.initialize();
        }
        return GifCacheManager.instance;
    }

    /**
     * 初始化缓存管理器
     */
    private async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            this.db = await DynamicPictureDb.getInstance({
                name: 'gif-cache',
                version: 1
            });
            this.isInitialized = true;
            Logger.info('GIF缓存管理器初始化成功');
        } catch (error) {
            Logger.error('GIF缓存管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取缓存的GIF帧数据
     * @param url 原始URL
     * @returns 缓存的GIF帧数据，如果没有缓存则返回null
     */
    public async getCachedGifFrames(url: string): Promise<CachedGifData | null> {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }

        // 如果不是有效的HTTP URL，不使用缓存
        if (!isValidHttpUrl(url) || isBlobUrl(url)) {
            return null;
        }

        const normalizedUrl = normalizeUrlForStorage(url);
        Logger.info(`查找缓存的GIF帧数据: ${url} -> ${normalizedUrl}`);

        try {
            const cachedData = await this.db.getData(normalizedUrl);
            if (cachedData) {
                // 验证缓存数据格式
                const validatedData = CachedGifDataSchema.parse(cachedData);
                Logger.info(`从缓存获取GIF帧数据: ${normalizedUrl}, 帧数: ${validatedData.frames.length}`);
                return validatedData;
            }
        } catch (error) {
            Logger.warn(`缓存读取失败或格式错误: ${normalizedUrl}`, error);
            // 如果缓存数据格式不正确，删除它
            await this.clearCache(url).catch(() => {});
        }

        return null;
    }

    /**
     * 缓存GIF帧数据
     * @param url 原始URL
     * @param frames 解析后的GIF帧数据
     * @returns 转换后的包含Blob的帧数据
     */
    public async cacheGifFrames(url: string, frames: GifFrame) {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }
        const normalizedUrl = normalizeUrlForStorage(url);
        try {
            Logger.info(`开始缓存GIF帧数据: ${normalizedUrl}, 帧数: ${frames.frames.length}`);
            // 验证数据格式
            const validatedData = CachedGifDataSchema.parse(frames);
            // 存储到IndexedDB
            await this.db.updateData({
                key: normalizedUrl,
                value: validatedData,
            });
            Logger.info(`GIF帧数据缓存成功: ${normalizedUrl}, 帧数: ${frames.frames.length}`);
        } catch (error) {
            Logger.error(`缓存GIF帧数据失败: ${normalizedUrl}`, error);
        }
    }

    /**
     * 获取原始GIF数据（用于首次解析）
     * @param url 原始URL
     * @returns ArrayBuffer数据
     */
    public async getGifData(url: string): Promise<ArrayBuffer> {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }

        // 直接从网络获取原始数据用于解析
        Logger.info(`从网络获取GIF数据用于解析: ${url}`);
        return this.fetchArrayBuffer(url);
    }

    /**
     * 创建Blob URL用于播放
     * @param url 原始URL
     * @returns Blob URL
     */
    public async createBlobUrl(url: string): Promise<string[]> {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }
        // 如果已经是blob URL，直接返回
        if (isBlobUrl(url)) {
            return [];
        }
        const normalizedUrl = normalizeUrlForStorage(url);
        // 检查是否已经创建了blob URL
        if (this.blobUrlManager.has(normalizedUrl)) {
            const existingBlobUrl = this.blobUrlManager.get(normalizedUrl)!;
            Logger.info(`使用已存在的Blob URL: ${normalizedUrl}`);
            return existingBlobUrl;
        }
        Logger.info(`查找缓存创建: ${normalizedUrl}`);
        try {
            // 从缓存获取blob
            const cachedData = await this.db.getData(normalizedUrl);
            if (cachedData) {
                Logger.info(`缓存命中，创建blob urls: ${normalizedUrl}`);
                const blobUrls: string[] = [];
                cachedData.frames.forEach((frame) => {
                    blobUrls.push(URL.createObjectURL(frame.frameBlob));
                });
                this.blobUrlManager.set(normalizedUrl, blobUrls);
                return blobUrls;
            } else {
                Logger.warn(`缓存命中失败，重新加载数据: ${normalizedUrl}`);
                const frames = await fullFrameToBlob(composeFullFrames(
                    decompressFrames(
                        parseGIF(await this.getGifData(url))
                        , true
                    )
                ));
                await this.cacheGifFrames(url, frames)
                const blobUrls: string[] = [];
                frames.frames.forEach((frame) => {
                    blobUrls.push(URL.createObjectURL(frame.frameBlob));
                });
                this.blobUrlManager.set(normalizedUrl, blobUrls);
                return blobUrls;
            }
        } catch (error) {
            Logger.warn(`创建Blob URL失败: ${normalizedUrl}`, error);
        }
        return [];
    }

    /**
     * 释放Blob URL
     * @param url 原始URL
     */
    public revokeBlobUrl(url: string): void {
        const normalizedUrl = normalizeUrlForStorage(url);
        const blobUrls = this.blobUrlManager.get(normalizedUrl);
        
        if (blobUrls) {
            blobUrls.forEach((blobUrl) => {
                URL.revokeObjectURL(blobUrl);
                Logger.info(`释放Blob URL: ${normalizedUrl} -> ${blobUrl}`);
            });
            this.blobUrlManager.delete(normalizedUrl);
        }
    }

    /**
     * 释放所有Blob URL
     */
    public revokeAllBlobUrls(): void {
        for (const [normalizedUrl, blobUrls] of this.blobUrlManager.entries()) {
            blobUrls.forEach((blobUrl) => {
                URL.revokeObjectURL(blobUrl);
                Logger.info(`释放Blob URL: ${normalizedUrl} -> ${blobUrl}`);
            });
        }
        this.blobUrlManager.clear();
        Logger.info('已释放所有Blob URL');
    }

    /**
     * 预加载GIF到缓存
     * @param url 原始URL
     */
    public async preloadGif(url: string): Promise<void> {
        try {
            await this.getGifData(url);
            Logger.info(`GIF预加载完成: ${url}`);
        } catch (error) {
            Logger.error(`GIF预加载失败: ${url}`, error);
        }
    }

    /**
     * 清除指定URL的缓存
     * @param url 原始URL
     */
    public async clearCache(url: string): Promise<void> {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }

        const normalizedUrl = normalizeUrlForStorage(url);
        
        try {
            // 释放blob URL
            this.revokeBlobUrl(url);
            
            // 删除缓存
            await this.db.deleteData(normalizedUrl);
            Logger.info(`清除GIF缓存: ${normalizedUrl}`);
        } catch (error) {
            Logger.error(`清除GIF缓存失败: ${normalizedUrl}`, error);
        }
    }

    /**
     * 清除所有缓存
     */
    public async clearAllCache(): Promise<void> {
        if (!this.isInitialized || !this.db) {
            throw new Error('GIF缓存管理器未初始化');
        }

        try {
            // 释放所有blob URL
            this.revokeAllBlobUrls();
            
            // 清除数据库
            await this.db.clear();
            Logger.info('已清除所有GIF缓存');
        } catch (error) {
            Logger.error('清除所有GIF缓存失败:', error);
        }
    }

    /**
     * 从URL获取ArrayBuffer
     */
    private async fetchArrayBuffer(url: string): Promise<ArrayBuffer> {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`获取GIF失败: ${response.status} ${response.statusText}`);
        }
        return await response.arrayBuffer();
    }

    /**
     * 销毁缓存管理器
     */
    public async destroy(): Promise<void> {
        this.revokeAllBlobUrls();
        
        if (this.db) {
            await this.db.close();
            this.db = null;
        }
        
        this.isInitialized = false;
        GifCacheManager.instance = null;
        Logger.info('GIF缓存管理器已销毁');
    }
}
