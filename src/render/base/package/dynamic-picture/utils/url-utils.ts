/**
 * URL处理工具函数
 */

/**
 * 标准化URL，去除query参数，保留协议、域名和路径
 * @param url 原始URL
 * @returns 标准化后的URL作为存储key
 */
export function normalizeUrlForStorage(url: string): string {
    try {
        const urlObj = new URL(url);
        // 保留协议、域名和路径，去除query和hash
        return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch (error) {
        // 如果URL解析失败，返回原始URL
        console.warn('URL解析失败，使用原始URL:', url);
        return url;
    }
}

/**
 * 检查URL是否为有效的HTTP/HTTPS URL
 * @param url 要检查的URL
 * @returns 是否为有效的HTTP/HTTPS URL
 */
export function isValidHttpUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
        return false;
    }
}

/**
 * 检查URL是否为blob URL
 * @param url 要检查的URL
 * @returns 是否为blob URL
 */
export function isBlobUrl(url: string): boolean {
    return url.startsWith('blob:');
}

/**
 * 检查URL是否为data URL
 * @param url 要检查的URL
 * @returns 是否为data URL
 */
export function isDataUrl(url: string): boolean {
    return url.startsWith('data:');
}
