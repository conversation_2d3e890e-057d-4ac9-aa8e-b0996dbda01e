/**
 * 渲染器相关类型定义
 */

import { GifFrame } from './gif-frame';

export interface RenderingContext {
    canvas: HTMLCanvasElement;
    ctx: CanvasRenderingContext2D;
    width: number;
    height: number;
}

export interface FrameRenderOptions {
    clearCanvas?: boolean;
    applyDisposal?: boolean;
    updateMainImage?: boolean;
}

export interface ValidationResult {
    isValid: boolean;
    error?: string;
}

export interface CanvasManager {
    createTempCanvas(width: number, height: number): HTMLCanvasElement;
    createImageDataFromFrame(ctx: CanvasRenderingContext2D, frame: GifFrame): ImageData;
    drawToCompositeCanvas(sourceCanvas: HTMLCanvasElement, frame: GifFrame): void;
    clearCanvas(): void;
    clearFrameArea(frame: GifFrame): void;
}

export interface FrameManager {
    validateFrameIndex(index: number): boolean;
    getCurrentFrame(): GifFrame | null;
    getFrameCount(): number;
    setCurrentFrame(index: number): void;
    nextFrame(): void;
    previousFrame(): void;
}
