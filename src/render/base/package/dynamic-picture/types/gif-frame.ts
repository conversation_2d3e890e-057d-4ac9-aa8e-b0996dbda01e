/**
 * GIF帧相关类型定义
 */

export interface GifFrame {
    frames: Array<{
        frameBlob: Blob;
        delay: number;
    }>;
    dims: {
        width: number;
        height: number;
    };
}

export interface GifInfo {
    frameCount: number;
    currentFrame: number;
    isPlaying: boolean;
    playbackSpeed: number;
    hasFrames: boolean;
}

export interface PlaybackOptions {
    speed: number;
    autoPlay: boolean;
    loop: boolean;
}

export enum DisposalMethod {
    NONE = 0,           // 不处理，保留
    KEEP = 1,           // 不处理，保留  
    RESTORE_BACKGROUND = 2,  // 恢复到背景色
    RESTORE_PREVIOUS = 3     // 恢复到前一帧
}
