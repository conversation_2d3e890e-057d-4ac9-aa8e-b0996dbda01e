/**
 * GIF相关常量定义
 */

export const GIF_CONSTANTS = {
    // 帧延迟相关
    MIN_FRAME_DELAY: 20,        // 最小帧延迟(ms)
    DEFAULT_FRAME_DELAY: 100,   // 默认帧延迟(ms)
    
    // 播放速度相关
    MIN_SPEED: 0.1,             // 最小播放速度
    MAX_SPEED: 5.0,             // 最大播放速度
    DEFAULT_SPEED: 1.0,         // 默认播放速度
    
    // GIF帧延迟单位转换
    DELAY_UNIT_MS: 10,          // GIF延迟单位：1/100秒 = 10ms
    DEFAULT_DELAY_UNITS: 10,    // 默认延迟：10个1/100秒
    
    // 画布相关
    CANVAS_ALPHA: true,         // 画布是否支持透明
    IMAGE_FORMAT: 'image/png',  // 导出图像格式
    
    // 调试相关
    DEBUG_ENABLED: false,       // 是否启用调试日志
} as const;

export const DISPOSAL_METHODS = {
    NONE: 0,                    // 不处理，保留
    KEEP: 1,                    // 不处理，保留  
    RESTORE_BACKGROUND: 2,      // 恢复到背景色
    RESTORE_PREVIOUS: 3,        // 恢复到前一帧
} as const;

export type DisposalMethod = typeof DISPOSAL_METHODS[keyof typeof DISPOSAL_METHODS];
