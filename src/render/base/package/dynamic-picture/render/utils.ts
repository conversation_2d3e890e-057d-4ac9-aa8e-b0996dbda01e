import { util } from "fabric";
import { IDynamicPicture } from "../dynamic-picture";

export function drawObjects(this: IDynamicPicture, ctx: CanvasRenderingContext2D) {
    for (const obj of this._objects) {
        if (this.canvas?.preserveObjectStacking && obj.group !== this) {
            ctx.save();
            ctx.transform(...util.invertTransform(this.calcTransformMatrix()));
            obj.render(ctx);
            ctx.restore();
        } else if (obj.group === this) {
            obj.render(ctx);
        }
    }
}