import { createIconImage } from "./constant";
import { IDynamicPicture } from "../dynamic-picture";
import { Point } from "fabric";
const showHeight = 44
const showWidth = 44

export async function createVideoTag(this: IDynamicPicture, ctx: CanvasRenderingContext2D) 
 {
    if (!this.canvas) return;
    if ((this.getScaledWidth() * this.canvas.getZoom()) < showWidth || (this.getScaledHeight() * this.canvas.getZoom()) < showHeight) return;
    const viewPort = this.canvas.viewportTransform;
    const iconImage = await createIconImage();
    const selfLeft = this.getScaledWidth() / 2;
    const selfTop =  -this.getScaledHeight() / 2;
    const selfPoint = new Point(selfLeft, selfTop)
    const transformedPoint = selfPoint.transform(viewPort)
    const iconWidth = 44
    const iconHeight = 44
    const scaledWidth = iconWidth
    const scaledHeight = iconHeight
    const left = transformedPoint.x - scaledWidth
    const top = transformedPoint.y
    ctx.save()
    ctx.drawImage(iconImage, left, top, scaledWidth, scaledHeight);
    ctx.restore();

}