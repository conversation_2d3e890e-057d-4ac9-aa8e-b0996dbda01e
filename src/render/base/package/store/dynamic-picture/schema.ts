import { z } from "zod";

export const DynamicPictureOptionShema = z.object({
  name: z.string(), // 数据库名称
  version: z.number(), // 数据库版本
})


// 缓存的GIF数据schema
export const CachedGifDataSchema = z.object({
  frames: z.array(z.object({
    delay: z.number(),
    frameBlob: z.instanceof(Blob),
  })),
  dims: z.object({
    width: z.number(),
    height: z.number(),
  }),
})

export const DynamicPictureDataSchema = z.object({
  key: z.string(), // 标准化后的URL作为key
  value: CachedGifDataSchema, // 存储解析后的GIF数据
})