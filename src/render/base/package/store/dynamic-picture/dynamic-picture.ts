import { openDB, type IDBPDatabase } from "idb";
import { z } from 'zod';
import Logger from "../../../../logger";
import { DynamicPictureOptionShema, DynamicPictureDataSchema, CachedGifDataSchema } from "./schema";

export class DynamicPictureDb {
  private static instance: DynamicPictureDb | null = null;
  private static initPromise: Promise<DynamicPictureDb> | null = null;
  
  public readonly options: z.infer<typeof DynamicPictureOptionShema>;
  private DB: IDBPDatabase | null = null;
  private isInitialized: boolean = false;

  private constructor(params: z.infer<typeof DynamicPictureOptionShema>) {
    this.options = DynamicPictureOptionShema.parse(params);
  }
  
  private async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      this.DB = await openDB(this.options.name, this.options.version, {
        upgrade: (db, _oldVersion, _newVersion, _transaction) => {
          // 创建对象存储，如果不存在的话
          if (!db.objectStoreNames.contains(this.options.name)) {
            db.createObjectStore(this.options.name);
            Logger.info(`Created object store: ${this.options.name}`);
          }
        },
      });
      this.isInitialized = true;
      Logger.info(`Database ${this.options.name} initialized successfully`);
    } catch (error) {
      Logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  static async getInstance(params: z.infer<typeof DynamicPictureOptionShema>): Promise<DynamicPictureDb> {
    // 如果已经有实例且参数相同，直接返回
    if (DynamicPictureDb.instance) {
      return DynamicPictureDb.instance;
    }

    // 如果正在初始化，等待初始化完成
    if (DynamicPictureDb.initPromise) {
      return DynamicPictureDb.initPromise;
    }

    // 创建新的初始化 Promise
    DynamicPictureDb.initPromise = (async () => {
      const instance = new DynamicPictureDb(params);
      await instance.initialize();
      DynamicPictureDb.instance = instance;
      return instance;
    })();

    try {
      const instance = await DynamicPictureDb.initPromise;
      return instance;
    } catch (error) {
      // 如果初始化失败，清理状态以便重试
      DynamicPictureDb.initPromise = null;
      DynamicPictureDb.instance = null;
      throw error;
    }
  }

  // 重置单例（主要用于测试）
  static reset(): void {
    DynamicPictureDb.instance = null;
    DynamicPictureDb.initPromise = null;
  }

  private ensureInitialized(): void {
    if (!this.isInitialized || !this.DB) {
      throw new Error('Database not initialized. Call getInstance() first.');
    }
  }

  public async setData(options: z.infer<typeof DynamicPictureDataSchema>): Promise<IDBValidKey> {
    this.ensureInitialized();
    
    const data = DynamicPictureDataSchema.parse(options);
    
    try {
      const result = await this.DB!.add(this.options.name, data.value, data.key);
      return result;
    } catch (error) {
      console.error('Failed to set data:', error);
      throw error;
    }
  }

  public async updateData(options: z.infer<typeof DynamicPictureDataSchema>): Promise<IDBValidKey> {
    this.ensureInitialized();
    
    const data = DynamicPictureDataSchema.parse(options);
    
    try {
      const result = await this.DB!.put(this.options.name, data.value, data.key);
      return result;
    } catch (error) {
      console.error('Failed to update data:', error);
      throw error;
    }
  }

  public async getData(key: string): Promise<z.infer<typeof CachedGifDataSchema> | undefined> {
    this.ensureInitialized();
    
    try {
      const result = await this.DB!.get(this.options.name, key);
      return result;
    } catch (error) {
      console.error('Failed to get data:', error);
      throw error;
    }
  }

  public async deleteData(key: string): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.DB!.delete(this.options.name, key);
    } catch (error) {
      console.error('Failed to delete data:', error);
      throw error;
    }
  }

  public async getAllData(): Promise<any[]> {
    this.ensureInitialized();
    
    try {
      const result = await this.DB!.getAll(this.options.name);
      return result;
    } catch (error) {
      console.error('Failed to get all data:', error);
      throw error;
    }
  }

  public async getAllKeys(): Promise<IDBValidKey[]> {
    this.ensureInitialized();
    
    try {
      const result = await this.DB!.getAllKeys(this.options.name);
      return result;
    } catch (error) {
      console.error('Failed to get all keys:', error);
      throw error;
    }
  }

  public async clear(): Promise<void> {
    this.ensureInitialized();
    
    try {
      await this.DB!.clear(this.options.name);
    } catch (error) {
      console.error('Failed to clear store:', error);
      throw error;
    }
  }

  public async close(): Promise<void> {
    if (this.DB) {
      this.DB.close();
      this.DB = null;
      this.isInitialized = false;
    }
  }

  // 获取数据库信息
  public getInfo(): { name: string; version: number; isInitialized: boolean } {
    return {
      name: this.options.name,
      version: this.options.version,
      isInitialized: this.isInitialized
    };
  }
}