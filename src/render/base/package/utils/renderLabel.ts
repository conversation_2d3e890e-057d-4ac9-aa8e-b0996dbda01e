import { Point, IText, util } from "fabric";
import { Frame } from "../frame/frame";

const paddingX = 8
const paddingY = 2
const labelGap = 8

export function getFontWidthAndHeight(this: Frame) {
    const text = new IText(this.labelText|| this.get('labelText'), {
        fontSize: this.labelFontSize,
        fontFamily: this.labelFontFamily,
    })
    text.initDimensions()
    return {
        width: (text.width + paddingX * 2),
        height: (text.height + paddingY * 2),
    }
}

export function getLabelBounds(this: Frame) {
    const centerPointer = this.getCenterPoint()
    const top = centerPointer.y - (this.getScaledHeight() / 2)
    const left = centerPointer.x - (this.getScaledWidth() / 2)
    const { width, height } = getFontWidthAndHeight.call(this)
    return {
        top,
        left,
        width,
        height
    }
}

export function getLabelBoundsByCanvas(this: Frame) {
    if (!this.canvas) return;
    const { left, top, height, width } = getLabelBounds.call(this);
    const startPoint = new Point(left, top);
    const endPoint = new Point(left + width, top + height);
    const viewPort = this.canvas.viewportTransform;
    const targetMatrix = util.invertTransform(viewPort)
    const transformedStart = startPoint.transform(targetMatrix);
    const transformedEnd = endPoint.transform(targetMatrix);
    const fixWidth = transformedEnd.x - transformedStart.x
    const fixHeight = transformedEnd.y - transformedStart.y
    const gap = labelGap / this.canvas.getZoom()
    return {
        left,
        top: top - fixHeight - gap,
        width: fixWidth,
        height: fixHeight
    }
}

export function isPointInLabelBounds(this: Frame, point: Point): boolean {
    if (!this.canvas) return false;
    
    // 检查基本条件
    if (!this.labelText || !this.showLabel || !this.get('labelText') || !this.get('showLabel')) {
        return false;
    }
    
    // 使用与渲染函数相同的边界计算方法
    const bounds = getLabelBoundsByCanvas.call(this);
    if (!bounds) return false;
    
    const { left, top, width, height } = bounds;
    
    return point.x >= left && point.x <= left + width &&
           point.y >= top && point.y <= top + height;
}

export function renderLabel(this: Frame, ctx: CanvasRenderingContext2D) {
    if (!this.canvas) return;
    if (
        !this.labelText ||
        !this.showLabel ||
        !this.get('labelText') ||
        !this.get('showLabel')
    ) return
    ctx.save();

    const bounds = getLabelBoundsByCanvas.call(this);
    if (!bounds) {
        ctx.restore();
        return;
    }

    const { left, top, height, width } = bounds;
    const zoom = this.canvas.getZoom();

    // Calculate scaled padding values
    const scaledPaddingX = paddingX / zoom;
    const round = 4 / zoom;

    ctx.fillStyle = '#1D1E23';
    ctx.beginPath();
    ctx.strokeStyle = '#22272E';
    if (ctx.roundRect) {
        ctx.roundRect(left, top, width, height, round);
    } else {
        ctx.rect(left, top, width, height);
    }
    ctx.fill();
    ctx.stroke();

    ctx.fillStyle = this.labelTextFill || 'white';
    ctx.font = `${(this.labelFontSize || 14) / zoom}px ${this.labelFontFamily || 'Arial'}`;
    ctx.textAlign = 'start';
    ctx.textBaseline = 'middle';
    ctx.fillText(
        this.get('labelText'),
        left + scaledPaddingX,
        top + height/2
    );

    ctx.restore();
}
