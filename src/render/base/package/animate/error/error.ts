import { FabricObject, Group, IText, Rect, classRegistry, loadSVGFromString, util } from 'fabric';
import { defaultErrorValues, errorSvg } from './constant';
import { Animate } from '../loader/animate';
import { ErrorProps } from './types';
import { ErrorPresetType } from '../../preset/error/type';
import { getErrorPreset } from '../../preset/error/error';

export class Error extends Group {
    static type = 'error';
    static ownDefaults = defaultErrorValues;
    static cacheProperties = Group.cacheProperties;
    private _loaderAnimate: Animate | null = null;
    private background: Rect | null = null;
    private preset: Omit<ErrorPresetType, 'baseEdge'>;

    static getDefaults(): Record<string, any> {
        return {
            ...super.getDefaults(),
            ...Error.ownDefaults,
        };
    }

    constructor(options: Partial<ErrorProps> & { message: string }) {
        const opts = { ...Error.ownDefaults, ...options };
        super([], opts);
        this.preset = getErrorPreset(this.getScaledWidth() || 200, this.getScaledHeight() || 200);
        this.createBackground();
        this.initSvg();
        this.initText(options.message);
    }

    initSvg = () => {
        loadSVGFromString(errorSvg).then(({ objects }) => {
            const group = util.groupSVGElements(objects.filter((item) => item !== null) as FabricObject[])
            group.set({
                left: this.getCenterPoint().x,
                top: this.getCenterPoint().y - group.getScaledHeight(),
                originX: 'center',
                originY: 'center',
                opacity: 0.2,
            })
            this.add(group)
            group.set({
                left: this.preset.iconLeft,
                top: this.preset.iconTop,
                scaleX: this.getScaledWidth() / 460,
                scaleY: this.getScaledWidth() / 460,
            })
        })
    }

    initText = (message: string) => {
        const text = new IText(message, {

            fontSize: this.preset.fontSize,
            fontFamily: 'Arial',
            fill: '#FFF',
            opacity: 0.5,
            angle: 0,
            originX: 'center',
            originY: 'center',
        })
        this.add(text);
        text.set({
            left: this.preset.fontLeft,
            top: this.preset.fontTop,
            angle: 0,
            originX: 'center',
            originY: 'center',
        })
    }


    private createBackground() {
        if (this.background) {
            this.remove(this.background);
        }
        this.background = new Rect({
            width: this.getScaledWidth() || 200,
            height: this.getScaledHeight() || 200,
            fill: 'black',
            opacity: 0.8,
            selectable: false,
            evented: false
        });
        this.add(this.background);
    }


    setSpeed(speed: number) {
        if (this._loaderAnimate) {
            this._loaderAnimate.setSpeed(speed);
        }
    }

    initialize(speed: number = 2) {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
        }

        if (this.background) {
            this._loaderAnimate = new Animate(this.background, speed);
            this._loaderAnimate.start();
        }

        this.canvas?.requestRenderAll();
    }

    dispose() {
        if (this._loaderAnimate) {
            this._loaderAnimate.stop();
            this._loaderAnimate = null;
        }
        super.dispose();
    }
}

classRegistry.setClass(Error);
