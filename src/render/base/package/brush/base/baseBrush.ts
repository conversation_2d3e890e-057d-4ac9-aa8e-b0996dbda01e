import {Canvas, FabricObject, Rect } from "fabric";
import { PencilBrush } from "fabric";
import { BaseBrushOptions, ExportOptions } from "../types";
import { createMaskContainer, createTitleAndButtonContainer } from "../utils";
import { Group } from "fabric";
import { exportShapesToBlob } from "../utils/exportShapesToBlob";
import { calculateTipPosition } from "../utils/tip";
import {ElementName, getViewPortVisibleArea, WarningType} from "../../../../utils";
import Logger from "../../../../logger";
import { triggerEvent } from "../utils/event";
import { SmartMask } from "../../mask";
import {Container} from "../../container/container";
import {
  renderMaskByCanvas,
  viewportTranslateByCanvasHandler,
   viewportZoomByCanvasHandler,
} from "../../../../plugins/utils/mask";

export abstract class IBaseBrush extends PencilBrush {
    // 承载图形容器
  public maskContainer: Group

  public titleAndButtonContainer: Group

  public stashShape: FabricObject[] = []

  public tipsEl: Group[] = []

  private _viewportTranslateHandler: any;
  private _viewportZoomHandler: any;

  private maskElement: Rect | undefined;
  private targetElementParent: Canvas | Container;
  private targetElementParentZIndex: number;
  private isReselecting: boolean = false; // 标志位，防止重复选中造成的闪烁
  private originalOnDeselect: Function | undefined; // 保存原始的onDeselect方法
  private isSettingDrawingMode: boolean = false; // 标志位，表示正在设置绘制模式
  private originalDiscardActiveObject: ((e?: any) => any) | undefined; // 保存原始的discardActiveObject方法



  constructor(canvas: Canvas, public options: BaseBrushOptions) {
    super(canvas);
    this.maskContainer = createMaskContainer.call(this.options.targetElement, {
        name: this.options.shapeContainerName,
        opacity: this.options.shapeContainerOpacity,
    })
    this.titleAndButtonContainer = createTitleAndButtonContainer.call(this.options.targetElement, {
        name: 'titleAndButtonContainer',
        opacity: this.options.shapeContainerOpacity,
        showTitle: this.options.showShapeTitle,
    })
    this.options.targetElement.bringObjectToFront(this.titleAndButtonContainer)

    // 保存原始层级信息（在移动对象之前）
    this.targetElementParent = (this.options.targetElement.parent ?? this.canvas ) as Container || this.canvas;
    this.targetElementParentZIndex = this.targetElementParent._objects.indexOf(this.options.targetElement);
    this.targetElementParent.remove(this.options.targetElement)
    this.canvas.add(this.options.targetElement)
    // 根据keepSelected选项决定是否取消选中状态
    if (!this.options.keepSelected) {
      this.options.targetElement.set("selectable", false);
      this.canvas.set('selection', false)
      this.canvas.discardActiveObject();
    } else {
      // 如果keepSelected为true，确保目标元素保持选中状态
      this.canvas.setActiveObject(this.options.targetElement);
      // 重写目标元素的onDeselect方法，防止被取消选中
      this.overrideTargetElementDeselect();
      // 重写Canvas的discardActiveObject方法，防止在绘制时清除选中状态
      this.overrideCanvasDiscardActiveObject();
    }
    if (options.isMask) {
      this._renderMask()

    }
    this.canvas.bringObjectToFront(this.options.targetElement)
    this.canvas.set('isDisabledContextMenu', true)
    this.bindEvent()
    // this.changeTargetStatus(true)
    this.canvas.renderAll()
  }


  _renderMask() {
    this.maskElement = renderMaskByCanvas.call(this.canvas, {
      maskColor: 'rgba(0, 0, 0, 0.5)',
      maskOpacity: 1,
    });
    this._viewportTranslateHandler = viewportTranslateByCanvasHandler.bind(this.maskElement, this.canvas);
    this._viewportZoomHandler = viewportZoomByCanvasHandler.bind(this.maskElement, this.canvas);
    this.bindEvent();
  }

  bindEvent = () => {
    this.canvas.on('object:added', this.objectAddedHandler)
    this.canvas.on('object:removed', this.objectRemovedHandler)
    if (this.options.keepSelected) {
      this.canvas.on('selection:cleared', this.selectionClearedHandler);
      this.canvas.on('before:selection:cleared', this.beforeSelectionClearedHandler);
    }
    if (this.options.isMask) {
      this.canvas.on('viewport:translate', this._viewportTranslateHandler!);
      this.canvas.on('viewport:zoom', this._viewportZoomHandler!);
    }
  }

  unbindEvent = () => {
    this.canvas.off('object:added', this.objectAddedHandler)
    this.canvas.off('object:removed', this.objectRemovedHandler)
    this.canvas.off('selection:cleared', this.selectionClearedHandler);
    this.canvas.off('before:selection:cleared', this.beforeSelectionClearedHandler);
    if (this.options.isMask) {
      this.canvas.off('viewport:translate', this.viewportTranslateHandler);
      this.canvas.off('viewport:zoom', this.viewportZoomHandler);
    }
  }

  objectAddedHandler = () => {
    if (this.options.isMask && this.maskElement) {
      this.canvas.bringObjectToFront(this.maskElement)
      this.canvas.bringObjectToFront(this.options.targetElement)
    }
  }

  objectRemovedHandler = () => {
    // 检查被删除的对象是否是目标元素
    window.requestAnimationFrame(() => {
      if (!this.options.targetElement.canvas) {
        // 目标元素被删除，自动销毁笔刷并退出绘制模式
        this.handleTargetElementRemoved();
      }
    })

  }

  /**
   * 处理目标元素被删除的情况
   */
  private handleTargetElementRemoved = () => {
    // 退出绘制模式
    this.canvas.isDrawingMode = false;
    this.canvas.freeDrawingBrush = undefined;

    // 清理笔刷资源
    this.cleanupBrushResources();

    // 触发自定义事件，通知外部笔刷已被销毁
    this.canvas.fire('brush:target:removed', {
      brush: this,
      targetElement: this.options.targetElement
    });
  }

  /**
   * 清理笔刷资源（通用清理，适用于正常销毁和目标元素删除两种情况）
   */
  private cleanupBrushResources = () => {
    // 恢复Canvas的原始方法
    this.restoreCanvasDiscardActiveObject();

    // 清理遮罩元素
    if (this.options.isMask && this.maskElement) {
      this.canvas.remove(this.maskElement);
      this.maskElement = undefined;
    }

    // 清理容器（如果不是受保护的）
    if (!this.options.isProtected) {
      this.titleAndButtonContainer.parent?.remove(this.titleAndButtonContainer);
    }

    // 设置容器为不可见
    this.titleAndButtonContainer.set({
      visible: false,
      selectable: false,
      evented: false,
      subTargetCheck: false,
    })

    // 解绑事件

    // 恢复画布设置
    this.canvas.set('isDisabledContextMenu', false);
    this.canvas.set('selection', true);
  }

  beforeSelectionClearedHandler = (e: any) => {
    // 在选中状态被清除之前，如果keepSelected为true且目标元素即将被取消选中，则阻止清除
    if (this.options.keepSelected && !this.isReselecting) {
      const activeObject = this.canvas.getActiveObject();
      if (activeObject === this.options.targetElement) {
        // 阻止目标元素被取消选中
        e.preventDefault && e.preventDefault();
        return false;
      }
    }
  }

  /**
   * 重写目标元素的onDeselect方法，防止在keepSelected模式下被取消选中
   */
  private overrideTargetElementDeselect = () => {
    // 保存原始的onDeselect方法
    this.originalOnDeselect = (this.options.targetElement as any).onDeselect;

    // 重写onDeselect方法
    (this.options.targetElement as any).onDeselect = () => {
      // 在keepSelected模式下，阻止目标元素被取消选中
      if (this.options.keepSelected) {
        return false;
      }
      // 否则调用原始的onDeselect方法
      return this.originalOnDeselect ? this.originalOnDeselect.call(this.options.targetElement) : true;
    };
  }

  /**
   * 重写Canvas的discardActiveObject方法，防止在keepSelected模式下清除选中状态
   */
  private overrideCanvasDiscardActiveObject = () => {
    // 保存原始的discardActiveObject方法
    this.originalDiscardActiveObject = this.canvas.discardActiveObject.bind(this.canvas);

    // 重写discardActiveObject方法
    (this.canvas as any).discardActiveObject = (e?: any) => {
      if (this.options.keepSelected) {
        const activeObject = this.canvas.getActiveObject();
        // 如果当前选中的是目标元素，则不执行discardActiveObject
        if (activeObject === this.options.targetElement) {
          return this.canvas;
        }
      }
      // 否则调用原始的discardActiveObject方法
      return this.originalDiscardActiveObject ? this.originalDiscardActiveObject(e) : this.canvas;
    };
  }

  /**
   * 恢复目标元素的原始onDeselect方法
   */
  private restoreTargetElementDeselect = () => {
    if (this.originalOnDeselect) {
      (this.options.targetElement as any).onDeselect = this.originalOnDeselect;
      this.originalOnDeselect = undefined;
    }
  }

  /**
   * 恢复Canvas的原始discardActiveObject方法
   */
  private restoreCanvasDiscardActiveObject = () => {
    if (this.originalDiscardActiveObject) {
      (this.canvas as any).discardActiveObject = this.originalDiscardActiveObject;
      this.originalDiscardActiveObject = undefined;
    }
  }

  /**
   * 安全地设置画布的绘制模式，避免闪烁
   * @param isDrawingMode 是否启用绘制模式
   */
  public setDrawingMode = (isDrawingMode: boolean) => {
    if (this.options.keepSelected) {
      this.isSettingDrawingMode = true;
      this.canvas.isDrawingMode = isDrawingMode;

      if (isDrawingMode) {
        // 在设置绘制模式后立即恢复选中状态
        setTimeout(() => {
          this.canvas.setActiveObject(this.options.targetElement);
          this.canvas.renderAll();
          this.isSettingDrawingMode = false;
        }, 0);
      } else {
        this.isSettingDrawingMode = false;
      }
    } else {
      this.canvas.isDrawingMode = isDrawingMode;
    }
  }

  selectionClearedHandler = (e: any) => {
    // 当选中状态被清除时，如果keepSelected为true，重新选中目标元素
    if (this.options.keepSelected && !this.isReselecting && !this.isSettingDrawingMode) {
      // 检查是否是目标元素被取消选中
      const deselectedObjects = e.deselected || [];
      const isTargetDeselected = deselectedObjects.some((obj: FabricObject) => obj === this.options.targetElement);

      if (isTargetDeselected) {
        this.isReselecting = true;
        // 使用requestAnimationFrame确保在下一帧执行，减少闪烁
        requestAnimationFrame(() => {
          if (this.options.targetElement.canvas) {
            this.canvas.setActiveObject(this.options.targetElement);
          }
          this.canvas.renderAll();
          this.isReselecting = false;
        });
      }
    }
  }

  private viewportTranslateHandler = () => {
    if (!this.maskElement) return
    // 保持遮罩层固定在画布中心，不随视口平移
    const viewportArea = getViewPortVisibleArea(this.canvas);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.maskElement.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    this.canvas.renderAll();
  }

  private viewportZoomHandler = () => {
    if (!this.maskElement) return
    // 保持遮罩层缩放比例与视口一致
    const viewportArea = getViewPortVisibleArea(this.canvas);
    const width = viewportArea.right - viewportArea.left;
    const height = viewportArea.bottom - viewportArea.top;
    this.maskElement.set({
      width,
      height,
      left: viewportArea.left + width / 2,
      top: viewportArea.top + height / 2,
    });
    this.canvas.renderAll();
  }


  getMasks = () => {
    return this.maskContainer.getObjects().filter(el => el.get('isMask'))
  }



  getMaskCount = () => {
    return this.maskContainer.getObjects().filter(el => el.get('isMask')).length
  }

  /**
   * 设置targetElement的subTargetCheck状态
   * @param subTargetCheck
   */
  private changeTargetStatus = (subTargetCheck: boolean) => {
    this.options.targetElement.subTargetCheck = subTargetCheck
  }

  /**
   * 校验mask数量是否超出限制
   * @returns
   */
  validMaskCount = () => {
    if (!this.options.maxMaskCount) return false
    const result = this.getMaskCount() >= this.options.maxMaskCount
    if (result) {
      this.canvas.fire('warning', {
        objects: this.maskContainer.getObjects(),
        target: this.options.targetElement,
        type: WarningType.MASK_MAX_COUNT,
      })
      Logger.warn(`mask数量超过限制，最大数量为${this.options.maxMaskCount}`)
    }
    return result
  }

  /**
   * 清除所有mask
   * TODO：需要记录历史
   */
  clearTargetElement = (clearContainer?: boolean) => {
    const els = this.maskContainer.getObjects().filter(el => el.get('isMask'))
    els.forEach(el => {
      switch(el._name_) {
        case ElementName.MASK_RECT:
          el.parent?.remove(el)
          this.canvas.fire('mask:rect:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_PATH:
          el.parent?.remove(el)
          this.canvas.fire('mask:path:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_POLYGON:
          el.parent?.remove(el)
          this.canvas.fire('mask:lasso:deleted', { target: el, element: this.options.targetElement })
          break
        case ElementName.MASK_SMART:
          el.parent?.remove(el)
          this.canvas.fire('mask:smart:deleted', { target: el, element: this.options.targetElement })
          break
      }
    })
    this.removeTip()
    if (clearContainer) {
      this.maskContainer.parent?.remove(this.maskContainer)
    }
    this.canvas.renderAll()
  }


  /**
   * 导出容器到blob
   * @param options
   * @returns
   */
  exportShapesToBlob = (options: ExportOptions) => {
    return exportShapesToBlob(this.maskContainer, options)
  }

  /**
   * 设置显示或隐藏指定id的shape
   * @param id
   * @param visible
   */
  setShowShape = (id: string, visible: boolean) => {
    if (visible) {
      const target = this.stashShape.find(obj => obj._id_ === id)
      if (target) {
        this.maskContainer.add(target)
        return target
      }
    } else {
      const target = this.maskContainer
      .getObjects()
      .find(obj => obj._id_ === id)
      if (target) {
        this.maskContainer.remove(target)
        this.stashShape.push(target)
        return target
      }
    }
  }

  /**
   * 导出容器到canvas ImageData
   * @returns
   */
  exportContainerToCanvas = (): ImageData | null => {
    const canvas = this.maskContainer.toCanvasElement()
    const ctx = canvas.getContext('2d')
    if (!ctx) return null
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    return imageData
  }
  /**
   * 设置擦除模式
   * @param isErase
   */
  setErase = (isErase: boolean) => {
    this.options.erase = isErase
  }
  /**
   * 添加智能mask
   * @param mask
   */
  addSmartMask = (mask: SmartMask) => {
    mask.set({
      _parent_id_: this.maskContainer._id_,
      _parent_name_: this.maskContainer._name_,
      _old_prompt_: '',
      _new_prompt_: '',
      _message_id_: '',
      _loading_element_: false,
      _custom_data_history_: {},
      _custom_data_: {},
      index: this.getMaskCount() + 1,
      showTitle: this.options.showShapeTitle,
      showCloseBtn: this.options.showShapeCloseBtn,
      erase: this.options.erase,
      isMask: true,
      maskType: 'smart'
    })
    this.maskContainer.add(mask)
    this.canvas.renderAll()
    this.canvas.fire('mask:smart:created', { target: mask, element: this.options.targetElement })
  }

  removeTip = () => {
    const tips = this.maskContainer.getObjects().filter(el => el.get('isMask'))
    this.titleAndButtonContainer.getObjects().forEach(el => {
        el.parent?.remove(el)
    })
    return tips
  }


  renderTip = () => {
    if (!this.options.showShapeTitle) return
    const tips = this.removeTip()
    Promise.all(tips.map(tip => {
      return calculateTipPosition(this.titleAndButtonContainer, tip)
    })).then(tips => {
      this.tipsEl = tips
      this.canvas.renderAll()
    })
  }

  triggerRemoveTip = (target: FabricObject) => {
    const isRemoveAction = target.get('isRemoveAction')
    const removeTarget = target.get('removeTarget')
    if (isRemoveAction && removeTarget) {
      const tip = this.tipsEl.find(el => el === target)
      tip?.parent?.remove(tip)
      this.setShowShape(removeTarget, false)
      triggerEvent.call(this.canvas, target, this.options.targetElement)
      this.maskContainer.getObjects().forEach((el, index) => {
        if (el.get('isMask')) {
          el.set({
            index: index + 1
          })
        }
      })
      this.renderTip()
      this.canvas.renderAll()
    }
  }



  destroy = () => {
    this.unbindEvent();

    // 先处理目标元素相关的清理
    this.cleanupTargetElement();

    // 再处理通用的资源清理
    this.cleanupBrushResources();

    this.canvas.renderAll()
  }

  /**
   * 清理目标元素相关的设置（仅在正常销毁时调用，元素删除时不调用）
   */
  private cleanupTargetElement = () => {
    // 根据keepSelected选项决定是否恢复选中状态
    if (!this.options.keepSelected) {
      this.options.targetElement.set("selectable", true);
    } else {
      // 如果是keepSelected模式，恢复目标元素的原始方法
      this.restoreTargetElementDeselect();
    }

    this.options.targetElement.set('subTargetCheck', false)
    this.changeTargetStatus(false)

    this.maskContainer.set({
      visible: false,
      selectable: false,
      evented: false,
      subTargetCheck: false,
    })
    if(!this.options.targetElement.canvas) return
    // 从当前父级中移除目标对象
    const currentTargetParent = this.options.targetElement.parent || this.canvas
    currentTargetParent.remove(this.options.targetElement)

    // 检查原始父级是否仍然存在且有效
    if (this.targetElementParent && this.targetElementParent._objects) {
      // 恢复到原始层级位置，使用insertAt方法
      // 确保索引在有效范围内
      const validIndex = Math.min(this.targetElementParentZIndex, this.targetElementParent._objects.length)
      this.targetElementParent.insertAt(validIndex, this.options.targetElement)
    } else {
      // 如果原始父级不存在，添加到画布根级
      this.canvas.add(this.options.targetElement)
    }
  }
}

