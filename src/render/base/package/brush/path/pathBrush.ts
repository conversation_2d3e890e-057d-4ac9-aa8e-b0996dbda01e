import { Canvas, Point, TEvent } from "fabric";
import { nanoid } from "nanoid";
import { IBaseBrush } from "../base/baseBrush";
import { PathBrushOptions } from "./type";
import { isPathMaxBounds } from "../utils/maxBounds";
import {ElementName, getElementOptions} from "../../../../utils";
import { getMouseDownTarget } from "../utils/getMouseDownTarget";
import { isCompletelyOutside } from "../utils/overflow";

export class PathBrush extends IBaseBrush {
    _isDrawing: boolean = false
    _pathLength: number = 0


    constructor(canvas: Canvas, public options: PathBrushOptions) {
        super(canvas, options)
    }

    onMouseDown(pointer: Point, e: TEvent): void {
        const target = getMouseDownTarget(this.canvas, e)
        if (target) {
            this.triggerRemoveTip(target)
            this.canvas.fire('mask:path:deleted', {
                target,
                element: this.options.targetElement
            })
            return
        }
        if (this.validMaskCount()) return;
        this._isDrawing = true;
        this.width = this.options.width / this.canvas.getZoom();
        super.onMouseDown(pointer, { e: e.e });
    }

    onMouseMove(pointer: Point): void {
        if (!this._isDrawing) return
        if (this.options.maxBoundsNumber && !isPathMaxBounds(this.createPath(this.convertPointsToSVGPath([...this['_points'], pointer])), this.options.maxBoundsNumber)) return
        this._addPoint(pointer)
        this._render()
    }

    onMouseUp({ e }: TEvent): boolean {
        if (!this.canvas._isMainEvent(e)) return true;
        if (!this._isDrawing) return false;
        this._isDrawing = false;
        this.drawStraightLine = false;
        this['oldEnd'] = undefined;
        this._finalizeAndAddPath();
        return false
    }

    _addPoint = (point: Point) => {
        const points = this['_points']
        if (points.length > 0) {
            const lastPoint = points[points.length - 1];
            const dx = point.x - lastPoint.x;
            const dy = point.y - lastPoint.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            this._pathLength += distance;  // 累加路径长度
            this.canvas.contextTop.lineDashOffset = -this._pathLength;
        }
        if (
            points.length > 1 &&
            point.eq(points[points.length - 1])
        ) {
            return false;
        }
        if (this.drawStraightLine && points.length > 1) {
            this['_hasStraightLine'] = true;
            points.pop();
        }
        points.push(point);
        return true;
    }

    // _render(ctx: CanvasRenderingContext2D = this.canvas.contextTop) {
    //     const points = this['_points']
    //     let p1 = points[0],
    //         p2 = points[1];
    //     this._saveAndTransform(ctx);
    //     ctx.beginPath();
    //     if (points.length === 2 && p1.x === p2.x && p1.y === p2.y) {
    //         const width = this.width / 1000;
    //         p1.x -= width;
    //         p2.x += width;
    //     }

    //     // 绘制外层黑色虚线
    //     // ctx.lineWidth = this.width;
    //     // ctx.lineCap = 'round';
    //     // ctx.lineJoin = 'round';
    //     // ctx.strokeStyle = '#000000';
    //     // ctx.setLineDash([10, 10]);
    //     // ctx.lineDashOffset = 0;
    //     ctx.strokeStyle = this.options.shapeColor;
    //     ctx.globalAlpha = this.options.shapeContainerOpacity
    //     ctx.beginPath();
    //     ctx.moveTo(p1.x, p1.y);
    //     for (let i = 1; i < points.length; i++) {
    //         PencilBrush.drawSegment(ctx, p1, p2);
    //         p1 = points[i];
    //         p2 = points[i + 1];
    //     }
    //     ctx.lineTo(p1.x, p1.y);
    //     ctx.stroke();

    //     // 绘制内层白色虚线
    //     // ctx.lineWidth = this.width - 2;
    //     // ctx.strokeStyle = '#ffffff';
    //     // ctx.setLineDash([10, 10]);
    //     // ctx.lineDashOffset = 10; // 偏移虚线，使其与黑色虚线错开

    //     // ctx.beginPath();
    //     // p1 = points[0];
    //     // p2 = points[1];
    //     // ctx.moveTo(p1.x, p1.y);
    //     // for (let i = 1; i < points.length; i++) {
    //     //     PencilBrush.drawSegment(ctx, p1, p2);
    //     //     p1 = points[i];
    //     //     p2 = points[i + 1];
    //     // }
    //     // ctx.lineTo(p1.x, p1.y);
    //     // ctx.stroke();

    //     // 绘制中间透明区域
    //     // ctx.lineWidth = this.width - 4;
    //     // ctx.setLineDash([]);
    //     // ctx.globalCompositeOperation = 'destination-out';
    //     // ctx.beginPath();
    //     // p1 = points[0];
    //     // p2 = points[1];
    //     // ctx.moveTo(p1.x, p1.y);
    //     // for (let i = 1; i < points.length; i++) {
    //     //     PencilBrush.drawSegment(ctx, p1, p2);
    //     //     p1 = points[i];
    //     //     p2 = points[i + 1];
    //     // }
    //     // ctx.lineTo(p1.x, p1.y);
    //     // ctx.stroke();

    //     ctx.restore();
    // }


    _render(ctx: CanvasRenderingContext2D = this.canvas.contextTop) {
        const points = this['_points']
        this.canvas.clearContext(ctx)
        this._saveAndTransform(ctx)
        ctx.beginPath()
        ctx.moveTo(points[0].x, points[0].y)
        if (this.options.erase) {
            ctx.strokeStyle = this.options.eraseShapeColor
            ctx.globalAlpha = this.options.eraseShapeOpacity
        } else {
            ctx.strokeStyle = this.options.shapeColor
            ctx.globalAlpha = this.options.shapeContainerOpacity
        }
        for (let i = 1; i < points.length; i++) {
            ctx.lineTo(points[i].x, points[i].y)
        }
        ctx.stroke()
        ctx.restore()
    }

    _finalizeAndAddPath() {
        const ctx = this.canvas.contextTop;
        ctx.closePath();

        if (this.decimate) {
            this['_points'] = this.decimatePoints(this['_points'], this.decimate);
        }
        const pathData = this.convertPointsToSVGPath(this['_points']);
        const target = this.createPath(pathData)
        target.set({
            _id_: nanoid(),
            _name_: ElementName.MASK_PATH,
            _parent_id_: this.maskContainer._id_,
            _parent_name_: this.maskContainer._name_,
            _old_prompt_: '',
            _new_prompt_: '',
            _message_id_: '',
            showTitle: this.options.showShapeTitle,
            showCloseBtn: this.options.showShapeCloseBtn,
            erase: this.options.erase,
            globalCompositeOperation: this.options.erase ? 'destination-out' : 'source-over',
            _loading_element_: false,
            _custom_data_history_: {},
            _custom_data_: {},
            stroke: this.options.shapeColor,
            index: this.getMaskCount() + 1,
            isMask: true,
            maskType: 'path',
            strokeWidth: this.width,
            strokeLineCap: 'round',
            strokeLineJoin: 'round',
        })
        if (isCompletelyOutside(target, this.maskContainer)) {
            this.canvas.requestRenderAll()
            this.canvas.clearContext(ctx)
            return
        }
        this.canvas.fire('mask:path:before', {
            target,
            element: this.options.targetElement
        })
        this.maskContainer.add(target)
        this.canvas.requestRenderAll()
        this.canvas.clearContext(ctx)
        this.canvas.fire('mask:path:created', {
            target,
            element: this.options.targetElement
        })
        this.renderTip()
    }

    setWidth(width: number) {
        this.options.width = width
    }
}
