import { FixedLayout, Group, LayoutManager, Rect } from "fabric"
import { nanoid } from "nanoid"

export function createMaskContainer(
    this: Group,
    opt: {
        name: string,
        opacity: number,
    }
) {
    const isExist = this.getObjects().find(obj => obj.get('maskContainerName') === opt.name) as Group | undefined
    if (isExist) {
        isExist.set({
            opacity: opt.opacity,
            visible: true,
            selectable: false,
            evented: true,
            subTargetCheck: true,
            hasBorders: false,
            hasControls: false,
            left: 0,
            top: 0,

        })
        isExist.clipPath?.set({
            width: this.getScaledWidth() * (this.parent?.scaleX ?? 1),
            height: this.getScaledHeight() *  (this.parent?.scaleY ?? 1),
            left: 0,
            top: 0,
        })
        this.canvas?.renderAll()
        return isExist
    }
    const maskContainer = new Group([], {
        layoutManager: new LayoutManager(new FixedLayout()),
        width: this.getScaledWidth() * (this.parent?.scaleX ?? 1),
        height: this.getScaledHeight() *  (this.parent?.scaleY ?? 1),
        opacity: opt.opacity,
        angle: this.getTotalAngle(),
        selectable: false,
        evented: false,
        subTargetCheck: false,
        hasBorders: false,
        hasControls: false,
        _id_: nanoid(),
        _parent_id_: this._id_,
        _name_: opt.name,
    })
    const clipPath = new Rect({
        width: this.width,
        height: this.height,
    })
    maskContainer.clipPath = clipPath
    this.add(maskContainer)
    maskContainer.set({
        maskContainerName: opt.name,
        isMaskContainer: true,
        left: 0,
        top: 0,
    })
    clipPath.set({
        left: 0,
        top: 0,
        width: this.getScaledWidth() * (this.parent?.scaleX ?? 1),
        height: this.getScaledHeight() *  (this.parent?.scaleY ?? 1),
    })
    return maskContainer
}


export function createTitleAndButtonContainer(
    this: Group,
    opt: {
        name: string,
        opacity: number,
        showTitle: boolean,
    }
) {
    const isExist = this.getObjects().find(obj => obj.get('maskContainerName') === opt.name) as Group | undefined
    if (isExist) {
        isExist.set({
            visible: opt.showTitle,
            selectable: false,
            evented: true,
            subTargetCheck: true,
            hasBorders: false,
            hasControls: false,
        })
        return isExist
    }
    const maskContainer = new Group([], {
        layoutManager: new LayoutManager(new FixedLayout()),
        width: this.width,
        height: this.height,
        scaleX: this.scaleX,
        scaleY: this.scaleY,
        opacity: opt.opacity,
        selectable: false,
        evented: true,
        subTargetCheck: true,
        hasBorders: false,
        hasControls: false,
    })
    const clipPath = new Rect({
        width: this.width,
        height: this.height,
        scaleX: this.scaleX,
        scaleY: this.scaleY,
    })
    maskContainer.clipPath = clipPath
    this.add(maskContainer)
    maskContainer.set({
        maskContainerName: opt.name,
        left: 0,
        top: 0,
    })
    clipPath.set({
        left: 0,
        top: 0,
    })
    return maskContainer
}
