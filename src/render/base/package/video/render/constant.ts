export const playIcon = "data:image/svg+xml;base64,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"

export let iconImage: any = undefined
export const gifIcon = "data:image/svg+xml;base64,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"
export let GifIconImage: any = undefined;
export const createIconImage = (): Promise<HTMLImageElement> => new Promise((resolve, reject) => {
    if (iconImage) return resolve(iconImage);
    iconImage = new Image()
    iconImage.src = playIcon
    iconImage.onload = () => resolve(iconImage);
    iconImage.onerror = () => reject(new Error('Failed to load icon image'));
})

export const createGifIcon = (): Promise<HTMLImageElement> => new Promise((resolve, reject) => {
    if (GifIconImage) return resolve(GifIconImage);
    GifIconImage = new Image()
    GifIconImage.src = gifIcon
    GifIconImage.onload = () => resolve(GifIconImage);
    GifIconImage.onerror = () => reject(new Error('Failed to load icon image'));
})

export const getVideoIcon = (frameType: 'video'| 'dynamicPicture') => {
    if (frameType === 'video') return createIconImage();
    return createGifIcon();
}
