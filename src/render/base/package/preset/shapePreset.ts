import { z } from 'zod'
import { merge } from 'es-toolkit'
import { ShapePresetSchema } from "./shapePreset.type"

export const ShapePreset: Record<string, any> & z.infer<typeof ShapePresetSchema> = {
    imageBackgroundColor: 'transparent',
    videoBackgroundColor: 'transparent',
    gifBackgroundColor: 'transparent',
}

export const setShapePreset = (options: Partial<Record<string, any>>) => {
    merge(ShapePreset, options)
}