/**
 * 圆形loading使用等比预设
 */
import { merge } from "lodash-es";
import { LoaderByCenterPresetType } from "./type";

export const LoaderByCenterPreset: LoaderByCenterPresetType = {
    fontSize: 14, // 字体大小
    baseEdge: 460, // 基础边长
    maxCircleEdge: 8, // 最大圆的半径
    minCircleEdge: 6, // 最小圆的半径
    circleGap: 15, // 圆与圆之间的距离
    circleTop: 217, // 圆距离容器的顶部距离
    fontTop: 35, // 字体距离容器顶部的距离
    fontLeft: 0, // 字体距离容器的左侧距离
}


export const setLoaderByCenterPreset = (preset: LoaderByCenterPresetType) => {
    merge(LoaderByCenterPreset, preset)
}

export const getLoaderByCenterSizeByEdge = (width: number, height: number) => {
    const edge = Math.min(width, height);
    const { baseEdge, maxCircleEdge, minCircleEdge, circleGap, fontSize, circleTop, fontTop, fontLeft } = LoaderByCenterPreset;
    const scale = edge / baseEdge;
    const maxScale = Math.max(width / baseEdge, height / baseEdge);
    const widthScale = width / baseEdge;
    const heightScale = height / baseEdge;
    return {
        maxCircleEdge: maxCircleEdge * scale,
        minCircleEdge: minCircleEdge * scale,
        circleGap: circleGap * maxScale,
        fontSize: fontSize * scale,
        circleTop: circleTop * heightScale,
        fontTop: fontTop * heightScale,
        fontLeft: fontLeft * widthScale,
    }
}
