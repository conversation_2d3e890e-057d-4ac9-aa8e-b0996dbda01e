import type { FabricObject, LayoutManager } from "fabric";
import { Group } from "fabric";
import type { Frame } from "./frame";
import { setClipPath } from "./util";
import { IImage } from "../image/image";

const sizeKey = ["width", "height"];
const autoLayoutKey = "autoLayout";
const clipKey = [autoLayoutKey, "clip", "strokeWidth", "$radius"];
const layoutManagerKey = "layoutManager";

 
export function _set(this: Frame | IImage, key: string, value: any) {
    const hasChangedSize = sizeKey.includes(key) && this.get(key) != value;
    if (key == layoutManagerKey) setLayoutManager.call(this, value);
    // -- before --
    const result = Group.prototype._set.call(this, key, value);
    // -- after --

    if (key == "dirty") {
        this._objects?.forEach((o) => {
            loopObjectBefore(o, (obj) => {
                obj.dirty = true;
            });
        });
    }
    if (hasChangedSize || clipKey.includes(key)) setClipPath.call(this);

    return result;
}


function setLayoutManager(this: Frame | IImage, value?: LayoutManager) {
    const lastValue = this.layoutManager;
    if (!lastValue) return;
    if (lastValue.strategy.constructor == value?.strategy.constructor) return;
    lastValue.unsubscribeTargets({ target: this, targets: this.getObjects() });
    value?.subscribeTargets({ target: this, targets: this.getObjects() });
}

function loopObjectBefore(obj: FabricObject, cb: (_: FabricObject) => void | boolean): boolean | void {
    let b = cb(obj);
    if (b) return true;
    if (!(obj as Group)._objects) return;
        for (const o of (obj as Group)._objects) {
            b = loopObjectBefore(o, cb);
            if (b) break;
        }
    return b;
}