import { Render } from "../render";
import { ElementName, ElementOptions, LoadingType } from "../utils";

export abstract class Plugin {
    public _render: Render;
    public _options?: Record<string, any>;
    constructor(_render: Render, _options?: Record<string, any>) {
        this._render = _render;
        this._options = _options;
    }
    public abstract __name__: string;
    public abstract __destroy__: () => void;
}
/**
 * 导出元素为图片选项
 *
 * @param includeType 导出元素类型
 * @param callBack 导出图片回调
 * @param exportType 导出图片类型
 * @param quality 导出图片质量 0-1
 * @param multiplier 导出图片放大倍数
 */
export type ExportElementToImageOptions = {
    includeType: Array<ElementName.IMAGE | ElementName.TEXT>,
    callBack?: (elements: ElementOptions) => ElementOptions,
    exportType?: 'png' | 'jpeg',
    quality?: number,
    multiplier?: number,
    merge?: boolean,
    applyParentScale?:boolean
}

export type LoadingText = {
    id: string;
    text: string;
    type: LoadingType;
    blur?: boolean;
    maskOpacity?: number;
}
