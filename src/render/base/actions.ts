import { Render } from '../render';
import { createLoading } from '../utils/shapes/loading';
import { ActiveSelection, FabricObject, Group, Point, util } from 'fabric';
import {
  createContainerElements,
  createElement,
  createError,
  findCustomGroupById,
  getMaxCoordsX,
  getMaxCoordsY,
  getMinCoordsX,
  getMinCoordsY,
  replaceImage,
} from '../utils/shapes';
import {
  AlignMode,
  ElementName,
  getElementOptions,
  ImageCustomOptions,
  ImageOptions,
  ElementOptions,
  LoadingType,
} from '../utils';
import { Canvas, IText } from 'fabric';
import { selectionElements } from '../utils/canvas';
import { ExportElementToImageOptions, LoadingText } from './types';
import { Error } from './package/animate/error';
import { IImage } from './package/image/image';
import { LoadingOptions } from '../utils/types';
import { replaceTextFill } from '../utils/shapes/replaceTextFill';
import { DefaultFontPreset, InFrameFontPreset } from './package/preset';
import { Frame } from './package/frame/frame';
import { createExportElement } from '../utils/exportElement/exportElement';
import { resetId } from '../utils/shapes/idHandler';
export class Actions {
  private _copyElement: FabricObject[] | null = null;
  public _copyPoint: Point | null = null;
  public _loadingEls: Map<string, LoadingOptions> = new Map();
  private _errorEls: Map<string, Error> = new Map();
  private _copyOptions?: ElementOptions[];

  constructor(private render: Render) { }

  public _destroy = () => {
    this._copyElement = null;
  };

  public get copyElement(): FabricObject[] | null {
    return this._copyElement;
  }

  /**
   * 组合元素
   */
  public mergeElements = (els?: FabricObject[]): Group | undefined => {
    if (!els) {
      els = this.render._FC.getActiveObjects();
    }
    const isFrameInEls = els.some((el) => el._name_ === ElementName.FRAME);
    if (isFrameInEls) return;
    els.forEach((el) => {
      if (el.group) {
        el.group.remove(el);
      }
      this.render._FC.remove(el);
    });
    const container = createContainerElements('', els, {
      id: els[0]._parent_id_,
    });
    this.render._FC.add(container);
    this.render._FC.setActiveObject(container);
    this.render._FC.requestRenderAll();
    return container;
  };
  /**
   * 拆分元素
   */
  public splitElements = (container: Group, isEnableCache: boolean = false) => {
    const objects = container.getObjects();
    objects.forEach((object) => {
      object.set({
        _parent_id_: '',
      });
    });
    if (isEnableCache) {
      const parentIndex = this.render._FC.getObjects().indexOf(container);
      if (parentIndex !== -1) return;
      const children = objects.map(el => ({
        element: el,
        zIndex: container.getObjects().indexOf(el)
      }))
      this.render._FC.remove(container);
      container.remove(...objects);
      children.forEach((child) => {
        this.render._FC.insertAt(child.zIndex + parentIndex, child.element);
        this.render._FC.fire('object:added', {
          target: child.element,
        });
      })
    } else {
      this.render._FC.add(...objects);
      this.render._FC.remove(container);
      container.remove(...objects);
    }
    
    const as = new ActiveSelection(objects, {
      canvas: this.render._FC,
    });
    this.render._FC.setActiveObject(as);
    this.render._FC.requestRenderAll();
  };
  /**
   * 设置对齐模式
   * @param mode 对齐模式
   */
  public alignMode = (mode: AlignMode) => {
    const objects = this.render._FC.getActiveObjects();
    if (objects.length <= 1) return;
    const { left, top, right, bottom, centerX, centerY } =
      this.render._Railings.RailingPosition;
    this.render._Railings.setContinueUpdateRailing(true);
    this.render._FC.discardActiveObject();
    switch (mode) {
      case AlignMode.LEFT:
        objects.forEach((obj) => {
          obj.setCoords();
          const minX = getMinCoordsX(obj);
          obj.set({
            left: left + (obj.left - minX),
          });
        });
        break;
      case AlignMode.RIGHT:
        objects.forEach((obj) => {
          obj.setCoords();
          const maxX = getMaxCoordsX(obj);
          obj.set({
            left: right - (maxX - obj.left),
          });
        });
        break;
      case AlignMode.CENTER:
        objects.forEach((obj) => {
          obj.setCoords();
          const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
          if (parent instanceof Canvas) {
            obj.set({
              left: centerX,
            });
          } else {
            const parentLeft = parent.getCenterPoint().x;
            obj.set({
              left: centerX - parentLeft,
            });
          }
        });
        break;
      case AlignMode.TOP:
        objects.forEach((obj) => {
          obj.setCoords();
          const minY = getMinCoordsY(obj);
          obj.set({
            top: top + (obj.top - minY),
          });
        });
        break;
      case AlignMode.BOTTOM:
        objects.forEach((obj) => {
          obj.setCoords();
          const maxY = getMaxCoordsY(obj);
          obj.set({
            top: bottom - (maxY - obj.top),
          });
        });
        break;
      case AlignMode.MIDDLE:
        objects.forEach((obj) => {
          obj.setCoords();
          const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
          if (parent instanceof Canvas) {
            obj.set({
              top: centerY,
            });
          } else {
            const parentTop = parent.getCenterPoint().y;
            obj.set({
              top: centerY - parentTop,
            });
          }
        });
        break;
    }
    const newAs = new ActiveSelection(objects, {
      canvas: this.render._FC,
    });
    this.render._FC.setActiveObject(newAs);
    this.render._Railings.setContinueUpdateRailing(false);
    this.render._FC.requestRenderAll();
  };
  /**
   * 复制元素
   * @param elements 元素
   */
  public copyStash = (...elements: FabricObject[]) => {
    // const minY = Math.min(
    //   ...elements.map((item) => item.getCenterPoint().x - item.width / 2)
    // );
    // const minX = Math.min(
    //   ...elements.map((item) => item.getCenterPoint().y - item.height / 2)
    // );
    // this._copyPoint = new Point(minX, minY);
    const object = this.render._FC.getActiveObject();
    if (!object) return;
    this._copyPoint = new Point(
      object.getCenterPoint().x - object.width / 2,
      object.getCenterPoint().y - object.height / 2
    );
    // this.render._FC.add(
    //   new Rect({
    //     width: 30,
    //     height: 30,
    //     left: this._copyPoint.x,
    //     top: this._copyPoint.y,
    //   })
    // );
    if (elements.length > 0) {
      this._copyElement = elements;
      this._copyOptions = this._copyElement.map((item => getElementOptions.bind(this.render)(item, { maskOptions: true })));
      this._copyOptions = this._copyOptions.map(item => {
        const target = this.render.Finder.findById(item._id_);
        if (target instanceof Canvas) return item;
        const options = util.qrDecompose(target.calcTransformMatrix());
        return {
          ...item,
          scaleX: options.scaleX,
          scaleY: options.scaleY,
          angle: options.angle,
        }
      })
      this._copyOptions = resetId(this._copyOptions);
      return;
    }
    this._copyElement = this.render._FC.getActiveObjects();
  };

  /**
   * 粘贴元素
   * @param position 粘贴位置
   */
  public paste = async (
    position: Point,
    isSelection: boolean = true,
    isReplaceTextFill: boolean = false,
    parent?: Group | Canvas
  ) => {
    if (!this._copyOptions) return;
    this._copyOptions = resetId(this._copyOptions);
    this._copyOptions = this._copyOptions.map((option) => {
      if (parent) {
        if (isReplaceTextFill) {
          if (parent instanceof Frame) {
            option = replaceTextFill(option, InFrameFontPreset.fill as string);
          } else {
            option = replaceTextFill(option, DefaultFontPreset.fill as string);
          }
        }
      }
      return option;
    })
    const offsetX = position.x - (this._copyPoint ?? { x: 0, y: 0 }).x;
    const offsetY = position.y - (this._copyPoint ?? { x: 0, y: 0 }).y;
    const elements = await Promise.all(this._copyOptions.map(async (option) => ({
      option,
      element: await createElement(option),
    })));
    elements.forEach(({ option, element }) => {
      let optionParent = parent;
          if (!parent) {
            optionParent = this.render.Finder.findById(
              option?._parent_id_
            ) as Group;
            if (optionParent) {
              optionParent.add(element);
            }
          } else {
            parent.add(element);
          }
    })
    elements.forEach(({ element }) => {
      element.set({
        left: element.left + offsetX,
        top: element.top + offsetY,
      });
    });
    this.render._FC.requestRenderAll();
    if (isSelection) {
      selectionElements(this.render._FC, elements.map(({ element }) => element));
    }
    return elements.map(({ element }) => element);
  };

  /**
   * 删除元素
   * @param elements 元素
   */
  public remove = (objects?: FabricObject[]) => {
    const activeObject = objects ?? this.render._FC.getActiveObjects();
    if (activeObject) {
      activeObject.forEach((obj) => {
        (obj.parent ?? this.render._FC).remove(obj);
      });
    }
    this.render._FC.discardActiveObject();
    this.render._FC.renderAll();
  };

  /**
   * 剪切元素
   */
  public cut = (objects?: FabricObject[]) => {
    const activeObjects = objects ?? this.render._FC.getActiveObjects();
    this.copyStash(...activeObjects);
    this.remove(activeObjects);
  };

  /**置顶 */
  public bringToFront = (objects?: FabricObject[]) => {
    const activeObject = objects ?? this.render._FC.getActiveObjects();
    activeObject.forEach((obj) => {
      const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
      if (parent) {
        (parent as Group).bringObjectToFront(obj);
      }
    });
    this.render._FC.requestRenderAll();
  };
  /**置底 */
  public sendToBack = (objects?: FabricObject[]) => {
    const activeObject = objects ?? this.render._FC.getActiveObjects();
    activeObject.forEach((obj) => {
      const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
      if (parent) {
        (parent as Group).sendObjectToBack(obj);
      }
    });
    this.render._FC.requestRenderAll();
  };
  /**向前移动 */
  public bringForward = (objects?: FabricObject[]) => {
    const activeObject = objects ?? this.render._FC.getActiveObjects();
    activeObject.forEach((obj) => {
      const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
      if (parent) {
        (parent as Group).bringObjectForward(obj);
      }
    });
    this.render._FC.requestRenderAll();
  };
  /**向后移动 */
  public sendBackward = (objects?: FabricObject[]) => {
    const activeObject = objects ?? this.render._FC.getActiveObjects();
    activeObject.forEach((obj) => {
      const parent = findCustomGroupById(this.render._FC, obj._parent_id_);
      if (parent) {
        (parent as Group).sendObjectBackwards(obj);
      }
    });
    this.render._FC.requestRenderAll();
  };

public sendZIndex = (object: FabricObject, zIndex: number) => {
    const parent = findCustomGroupById(this.render._FC, object._parent_id_);
    if (!parent) return;

    if (parent instanceof Frame) {
        // 使用 Frame 的专用方法
        (parent as Frame).moveChildToIndex(object, zIndex);
    } else {
        // 原有逻辑
        const objects = (parent as Group).getObjects();
        const index = objects.indexOf(object);
        if (index === -1) return;
        const validZIndex = Math.max(0, Math.min(zIndex, objects.length - 1));
        const array = (parent as Group)._objects;
        array.splice(index, 1);
        array.splice(validZIndex, 0, object);
    }
    
    this.render._FC.renderAll();
}


  public setError = (el: string, message = '生成失败') => {
    const element = this.render.Finder.findById(el) as Group;
    const error = createError(this.render._FC, element, message);
    if (error) {
      this._errorEls.set(element._id_, error);
      this.render._FC.requestRenderAll();
    }
  };

  /**
   * 设置取消错误
   */
  public setCancelError = (...els: string[]) => {
    const elements = els.map((el) =>
      this.render.Finder.findById(el)
    ) as Group[];
    elements.forEach((el) => {
      const error = this._errorEls.get(el._id_);
      if (error) {
        error.dispose();
        (el as Group).remove(error);
        this.render._FC.requestRenderAll();
      }
      this._errorEls.delete(el._id_);
    });
  };



  /**
   * 设置元素为加载中
   * @param targets 元素
   */
  public setLoading = (...targets: Array<LoadingText>) => {
    const elements = targets.map((item) => ({
      el: this.render.Finder.findById(item.id),
      text: item.text,
      type: item.type,
      blur: item.blur,
      maskOpacity: item.maskOpacity,
    })
    ) as { el: IImage, text: string, type: LoadingType, blur?: boolean, maskOpacity?: number }[];
    elements.forEach((elements) => {
      if (elements && elements.el.type === 'iimage' && elements.el.get('_loading_') !== true) {
        elements.el.set('_loading_', true);
        elements.el.set('_loading_text_', elements.text);
        const loadingOptions = createLoading(this.render._FC, {
          el: elements.el,
          type: elements.type,
          text: elements.text,
          blur: elements.blur,
          maskOpacity: elements.maskOpacity,
        })
        if (loadingOptions.loadingTarget) {
          this._loadingEls.set(elements.el._id_, loadingOptions);
        }
      }
    });
  };
  /**
   * 设置元素为加载完成
   * @param els 元素
   */
  public setLoaded = (...els: string[]) => {
    const elements = els
      .map((el) => this.render.Finder.findById(el))
      .filter((el) => !(el instanceof Canvas)) as FabricObject[];
    elements.forEach((el) => {
      if (el && el.type === 'iimage') {
        el.set('_loading_', false);
        el.set('_loading_text_', '');
        el.set('_loading_type_', '');
        const id = el._id_;
        if (this._loadingEls.has(id)) {
          const loader = this._loadingEls.get(id);
          if (loader) {
            loader.loadingTarget.dispose();
            (el as Group).remove(loader.loadingTarget);
            this.render._FC.requestRenderAll();
          }
          this._loadingEls.delete(id);
        }
      }
    });
  };
  /**
   * 获取加载中的元素
   * @returns 加载中的元素
   */
  public getLoadingElsByIds = () => {
    return Array.from(this._loadingEls.keys());
  };



  public replaceImage = async (
    element: Group,
    options: Partial<ImageCustomOptions & ImageOptions>,
    origin: 'lt' | 'rt' | 'lb' | 'rb' | 'center' = 'center',
    signal?: AbortSignal
  ) => {
    await replaceImage.call(this.render, element, options, origin, signal);
    this.render._FC.requestRenderAll();
  };


  public exportElementToImage = async (
    elements: Array<Group | IText>,
    options: ExportElementToImageOptions
  ) => {
    const { exportType, quality, multiplier } = options;

    const els = await createExportElement.call(this.render, elements, options);
    return els.map(el => el.toDataURL({
      format: exportType,
      quality: quality,
      multiplier: multiplier,
    }));
  };

  public exportElementToCanvas = async (
    elements: Array<Group | IText>,
    options: ExportElementToImageOptions
  ) => {
    const { multiplier } = options;
    const els = await createExportElement.call(this.render, elements, options);
    return els.map(el => el.toCanvasElement({ multiplier }));
  };

  public exportElementToBlob = async (
    elements: Array<Group | IText>,
    options: ExportElementToImageOptions,
    size?: { width: number, height: number }
  ) => {
    const { exportType, quality } = options;
    const mimeType = `image/${exportType ?? 'png'}`;
    const can = await this.exportElementToCanvas(elements, options);
    const promises = can.map(can => {
      if (size) {
        can.style.width = size.width + 'px';
        can.style.height = size.height + 'px';
      }
      return new Promise<Blob>((resolve, reject) => {
        can.toBlob(blob => {
          if (blob) {
            resolve(blob)
            return;
          }
          reject('导出失败');
        }, mimeType, quality);
      })
    })
    return Promise.all(promises);
  };
}
