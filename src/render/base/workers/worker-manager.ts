/**
 * Worker管理器 - 用于在render包内部使用worker
 */
import workerpool from 'workerpool'
import Logger from '../../logger'

export interface GifFrameData {
    frames: Array<{
        frameBlob: Blob;
        delay: number;
    }>;
    dims: {
        width: number;
        height: number;
    };
}

export type WorkerType = 'image' | 'gif'

export class WorkerManager {
    private static instance: WorkerManager | null = null
    private pools: Map<WorkerType, workerpool.WorkerPool> = new Map()
    private initializedWorkers: Set<WorkerType> = new Set()

    private constructor() {}

    /**
     * 获取WorkerManager单例
     */
    public static getInstance(): WorkerManager {
        if (!WorkerManager.instance) {
            WorkerManager.instance = new WorkerManager()
        }
        return WorkerManager.instance
    }

    /**
     * 初始化指定类型的worker
     * @param workerType worker类型
     * @param workerUrl worker文件的URL，如果不提供则尝试自动检测
     */
    public async initializeWorker(workerType: WorkerType, workerUrl?: string): Promise<void> {
        if (this.initializedWorkers.has(workerType)) {
            return
        }

        try {
            let finalWorkerUrl = workerUrl

            if (!finalWorkerUrl) {
                // 尝试自动检测worker文件路径
                finalWorkerUrl = this.detectWorkerUrl(workerType)
            }

            if (finalWorkerUrl) {
                const pool = workerpool.pool(finalWorkerUrl, {
                    minWorkers: 1,
                    maxWorkers: 2,
                    workerType: 'web'
                })
                this.pools.set(workerType, pool)
                this.initializedWorkers.add(workerType)
                Logger.info(`${workerType} Worker初始化成功:`, finalWorkerUrl)
            } else {
                throw new Error(`无法找到${workerType} worker文件`)
            }
        } catch (error) {
            Logger.error(`${workerType} Worker初始化失败:`, error)
            throw error
        }
    }

    /**
     * 自动检测worker文件URL
     */
    private detectWorkerUrl(workerType: WorkerType): string | null {
        try {
            const workerFileName = `${workerType}-worker.iife.js`

            // 尝试多种可能的路径
            const possiblePaths = [
                // npm包中的路径
                `./worker/${workerFileName}`,
                `../worker/${workerFileName}`,
                `../../worker/${workerFileName}`,
                // 开发环境路径
                `/worker/${workerFileName}`,
                // CDN或其他路径
                new URL(`../../../worker/${workerFileName}`, import.meta.url).href
            ]

            // 返回第一个可能的路径，实际使用时可能需要更智能的检测
            return possiblePaths[possiblePaths.length - 1]
        } catch (error) {
            Logger.warn(`自动检测${workerType} worker路径失败:`, error)
            return null
        }
    }

    /**
     * 获取指定类型的worker pool
     */
    private getWorkerPool(workerType: WorkerType): workerpool.WorkerPool {
        const pool = this.pools.get(workerType)
        if (!pool) {
            throw new Error(`${workerType} Worker未初始化，请先调用initializeWorker('${workerType}')`)
        }
        return pool
    }

    /**
     * 加载GIF数据
     * @param url GIF文件URL
     * @returns 解析后的GIF帧数据
     */
    public async loadGifData(url: string): Promise<GifFrameData> {
        // 确保gif worker已初始化
        if (!this.initializedWorkers.has('gif')) {
            await this.initializeWorker('gif')
        }

        const pool = this.getWorkerPool('gif')

        try {
            Logger.info('开始使用gif worker加载GIF:', url)
            const result = await pool.exec('loadGifData', [url])
            Logger.info('gif worker加载GIF完成:', url)
            return result
        } catch (error) {
            Logger.error('gif worker加载GIF失败:', error)
            throw error
        }
    }

    /**
     * 处理图像数据反转
     * @param data ImageData
     * @param rgb RGB颜色值
     * @returns 处理后的ImageData
     */
    public async imageDataInvertByMaskImage(
        data: ImageData,
        rgb: [number, number, number]
    ): Promise<ImageData> {
        // 确保image worker已初始化
        if (!this.initializedWorkers.has('image')) {
            await this.initializeWorker('image')
        }

        const pool = this.getWorkerPool('image')

        try {
            return await pool.exec('imageDataInvertByMaskImage', [data, rgb])
        } catch (error) {
            Logger.error('图像数据反转失败:', error)
            throw error
        }
    }

    /**
     * 检测ImageData是否有透明通道
     * @param data ImageData
     * @returns 是否有透明通道
     */
    public async isImageDataHasTransparent(data: ImageData): Promise<boolean> {
        // 确保image worker已初始化
        if (!this.initializedWorkers.has('image')) {
            await this.initializeWorker('image')
        }

        const pool = this.getWorkerPool('image')

        try {
            return await pool.exec('isImageDataHasTransparent', [data])
        } catch (error) {
            Logger.error('检测透明通道失败:', error)
            throw error
        }
    }

    /**
     * 使用mask图抠图
     * @param initImage 原始图像
     * @param maskImage 遮罩图像
     * @returns 抠图结果
     */
    public async cutoutWithMask(
        initImage: ImageData,
        maskImage: ImageData
    ): Promise<ImageData> {
        // 确保image worker已初始化
        if (!this.initializedWorkers.has('image')) {
            await this.initializeWorker('image')
        }

        const pool = this.getWorkerPool('image')

        try {
            return await pool.exec('cutoutWithMask', [initImage, maskImage])
        } catch (error) {
            Logger.error('mask抠图失败:', error)
            throw error
        }
    }

    /**
     * 销毁指定类型的worker pool
     * @param workerType 要销毁的worker类型，如果不指定则销毁所有
     */
    public async destroyWorker(workerType?: WorkerType): Promise<void> {
        if (workerType) {
            const pool = this.pools.get(workerType)
            if (pool) {
                try {
                    await pool.terminate()
                    this.pools.delete(workerType)
                    this.initializedWorkers.delete(workerType)
                    Logger.info(`${workerType} Worker已销毁`)
                } catch (error) {
                    Logger.error(`销毁${workerType} Worker失败:`, error)
                }
            }
        } else {
            // 销毁所有worker
            for (const [type, pool] of this.pools.entries()) {
                try {
                    await pool.terminate()
                    Logger.info(`${type} Worker已销毁`)
                } catch (error) {
                    Logger.error(`销毁${type} Worker失败:`, error)
                }
            }
            this.pools.clear()
            this.initializedWorkers.clear()
        }
    }

    /**
     * 检查指定worker是否已初始化
     */
    public isWorkerInitialized(workerType: WorkerType): boolean {
        return this.initializedWorkers.has(workerType)
    }

    /**
     * 获取已初始化的worker类型列表
     */
    public getInitializedWorkers(): WorkerType[] {
        return Array.from(this.initializedWorkers)
    }
}

// 导出单例实例
export const workerManager = WorkerManager.getInstance()
