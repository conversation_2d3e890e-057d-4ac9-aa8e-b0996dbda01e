/**
 * Worker管理器 - 用于在render包内部使用worker
 */
import workerpool from 'workerpool'
import Logger from '../../logger'

export interface GifFrameData {
    frames: Array<{
        frameBlob: Blob;
        delay: number;
    }>;
    dims: {
        width: number;
        height: number;
    };
}

export class WorkerManager {
    private static instance: WorkerManager | null = null
    private pool: workerpool.WorkerPool | null = null
    private isInitialized = false

    private constructor() {}

    /**
     * 获取WorkerManager单例
     */
    public static getInstance(): WorkerManager {
        if (!WorkerManager.instance) {
            WorkerManager.instance = new WorkerManager()
        }
        return WorkerManager.instance
    }

    /**
     * 初始化worker pool
     * @param workerUrl worker文件的URL，如果不提供则尝试自动检测
     */
    public async initialize(workerUrl?: string): Promise<void> {
        if (this.isInitialized && this.pool) {
            return
        }

        try {
            let finalWorkerUrl = workerUrl

            if (!finalWorkerUrl) {
                // 尝试自动检测worker文件路径
                finalWorkerUrl = this.detectWorkerUrl()
            }

            if (finalWorkerUrl) {
                this.pool = workerpool.pool(finalWorkerUrl, {
                    minWorkers: 1,
                    maxWorkers: 2,
                    workerType: 'web'
                })
                this.isInitialized = true
                Logger.info('WorkerManager初始化成功:', finalWorkerUrl)
            } else {
                throw new Error('无法找到worker文件')
            }
        } catch (error) {
            Logger.error('WorkerManager初始化失败:', error)
            throw error
        }
    }

    /**
     * 自动检测worker文件URL
     */
    private detectWorkerUrl(): string | null {
        try {
            // 尝试多种可能的路径
            const possiblePaths = [
                // npm包中的路径
                './worker/workers.iife.js',
                '../worker/workers.iife.js',
                '../../worker/workers.iife.js',
                // 开发环境路径
                '/worker/workers.iife.js',
                // CDN或其他路径
                new URL('../../../worker/workers.iife.js', import.meta.url).href
            ]

            // 返回第一个可能的路径，实际使用时可能需要更智能的检测
            return possiblePaths[possiblePaths.length - 1]
        } catch (error) {
            Logger.warn('自动检测worker路径失败:', error)
            return null
        }
    }

    /**
     * 加载GIF数据
     * @param url GIF文件URL
     * @returns 解析后的GIF帧数据
     */
    public async loadGifData(url: string): Promise<GifFrameData> {
        if (!this.isInitialized || !this.pool) {
            throw new Error('WorkerManager未初始化，请先调用initialize()')
        }

        try {
            Logger.info('开始使用worker加载GIF:', url)
            const result = await this.pool.exec('loadGifData', [url])
            Logger.info('worker加载GIF完成:', url)
            return result
        } catch (error) {
            Logger.error('worker加载GIF失败:', error)
            throw error
        }
    }

    /**
     * 处理图像数据反转
     * @param data ImageData
     * @param rgb RGB颜色值
     * @returns 处理后的ImageData
     */
    public async imageDataInvertByMaskImage(
        data: ImageData, 
        rgb: [number, number, number]
    ): Promise<ImageData> {
        if (!this.isInitialized || !this.pool) {
            throw new Error('WorkerManager未初始化，请先调用initialize()')
        }

        try {
            return await this.pool.exec('imageDataInvertByMaskImage', [data, rgb])
        } catch (error) {
            Logger.error('图像数据反转失败:', error)
            throw error
        }
    }

    /**
     * 检测ImageData是否有透明通道
     * @param data ImageData
     * @returns 是否有透明通道
     */
    public async isImageDataHasTransparent(data: ImageData): Promise<boolean> {
        if (!this.isInitialized || !this.pool) {
            throw new Error('WorkerManager未初始化，请先调用initialize()')
        }

        try {
            return await this.pool.exec('isImageDataHasTransparent', [data])
        } catch (error) {
            Logger.error('检测透明通道失败:', error)
            throw error
        }
    }

    /**
     * 使用mask图抠图
     * @param initImage 原始图像
     * @param maskImage 遮罩图像
     * @returns 抠图结果
     */
    public async cutoutWithMask(
        initImage: ImageData, 
        maskImage: ImageData
    ): Promise<ImageData> {
        if (!this.isInitialized || !this.pool) {
            throw new Error('WorkerManager未初始化，请先调用initialize()')
        }

        try {
            return await this.pool.exec('cutoutWithMask', [initImage, maskImage])
        } catch (error) {
            Logger.error('mask抠图失败:', error)
            throw error
        }
    }

    /**
     * 销毁worker pool
     */
    public async destroy(): Promise<void> {
        if (this.pool) {
            try {
                await this.pool.terminate()
                this.pool = null
                this.isInitialized = false
                Logger.info('WorkerManager已销毁')
            } catch (error) {
                Logger.error('销毁WorkerManager失败:', error)
            }
        }
    }

    /**
     * 检查是否已初始化
     */
    public get initialized(): boolean {
        return this.isInitialized
    }
}

// 导出单例实例
export const workerManager = WorkerManager.getInstance()
