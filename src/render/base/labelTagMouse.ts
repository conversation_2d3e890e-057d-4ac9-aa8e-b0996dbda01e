import { Point, TPointerEventInfo, TPointerEvent } from 'fabric';
import { Frame } from './package/frame/frame';
import { Render, ElementOptions, getElementOptions} from "..";
import { HistoryPlugins } from "../plugins/renderHistory";

export class LabelTagMouse {
    private isDraggingLabel: boolean = false;
    private _lastPointer: Point | null = null;
    private _lastFrame: Frame | null = null;

    private _beforeOp: ElementOptions[] = []
    constructor(private render: Render) {

        this.init();
    }

    private init = () => {
        this.render._FC.on('mouse:down', this._onMouseDown);
        this.render._FC.on('mouse:move', this._onMouseMove);
        this.render._FC.on('mouse:up', this._onMouseUp);
    }
    private unInit = () => {
        this.render._FC.off('mouse:down', this._onMouseDown);
        this.render._FC.off('mouse:move', this._onMouseMove);
        this.render._FC.off('mouse:up', this._onMouseUp);
    }


    private _onMouseDown = (event: TPointerEventInfo<TPointerEvent> & { alreadySelected?: boolean }) => {
        const pointer = this.render._FC.getScenePoint(event.e as PointerEvent);
        const canvasAllObjects = this.render._FC.getObjects();

        // 创建一个包含所有 Frame 对象的数组，按照 z-index 排序（从高到低）
        const frames = canvasAllObjects
            .filter(obj => obj instanceof Frame)
            .sort((a, b) => canvasAllObjects.indexOf(b) - canvasAllObjects.indexOf(a)) as Frame[];

        // 检查每个 Frame 的标签是否被点击
        for (const frame of frames) {
            if (frame.isPointInLabel(pointer)) {
                this._lastFrame = frame;
                this.render._FC.setActiveObject(frame);
                this.render._FC.requestRenderAll();
                this._lastPointer = pointer;
                this.isDraggingLabel = true;
                this.render._FC.selection = false;
                this._beforeOp = [getElementOptions.call(this.render, frame)];
                (event.e as PointerEvent).preventDefault();
                (event.e as PointerEvent).stopPropagation();
                return; // 一旦找到目标并处理，就退出循环
            }
        }
    }

    private _onMouseMove = (event: TPointerEventInfo<PointerEvent>) => {
        if (!this.isDraggingLabel || !this._lastPointer) return
        const currentPointer = this.render._FC.getScenePoint(event.e);
        const dx = currentPointer.x - this._lastPointer.x;
        const dy = currentPointer.y - this._lastPointer.y;
        this._lastFrame?.set('left', this._lastFrame.left + dx);
        this._lastFrame?.set('top', this._lastFrame.top + dy);
        this._lastFrame?.setCoords();
        this._lastPointer = currentPointer;
        this.render._FC.renderAll();
        event.e.preventDefault();
        event.e.stopPropagation();
    }

    private _onMouseUp = () => {
        if (!this.isDraggingLabel) return
        this.isDraggingLabel = false;
        this.render._FC.selection = true;
        if (this._lastFrame) {
            const _history = Array.from(this.render.getPlugins().values()).find(item => item instanceof HistoryPlugins)
            const afterElementOptions = [getElementOptions.call(this.render, this._lastFrame)]
            const operation = _history?.baseAction.getModifiedOperation({
                beforeData: this._beforeOp,
                afterData: afterElementOptions,
                selectable: false,
            })
            this._beforeOp = [];
            if (!operation) return;
            _history?.history.submit(operation);
        }
        this._lastFrame = null;
        this._lastPointer = null;
        this.render._FC.requestRenderAll();
    }

    public _destroy = () => {
        this.unInit();
    }
}
