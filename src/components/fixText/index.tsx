import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { useRenderStore } from "@/store";
import { useCallback, useEffect, useState } from "react";
import { FileImage, FileType, LassoSelect, Paintbrush, SquareDashed, Trash } from 'lucide-react'
import { ElementName } from "@/render";
import { WrapperTooltip } from "../tooltip";
import { RectBrush, PathBrush, LassoBrush, RenderMaskCursor } from "@/render";
import { IImage } from "@/render/base/package/image/image";
export default function FixText() {
  const [isImage, setIsImage] = useState<boolean>(false)
  const [ brush, setBrush ] = useState<PathBrush | RectBrush | LassoBrush | undefined>(undefined)
  const { render, renderStyle } = useRenderStore();
  const [isEraseMode, setIsEraseMode] = useState<boolean>(false)

  const selectionHandler = useCallback(() => {
    const selected = render?._FC.getActiveObject()
    if (selected?._name_ === ElementName.IMAGE) {
      setIsImage(true)
    } else {
      setIsImage(false)
    }
}, [render])


  useEffect(() => {
      render?._FC.on('selection:created', selectionHandler)
      render?._FC.on('selection:updated', selectionHandler)
      return () => {
          render?._FC.off('selection:created', selectionHandler)
          render?._FC.off('selection:updated', selectionHandler)
      }
  }, [render, selectionHandler])


  const closeOtherTool = () => {
    if (!brush || !render) return
    render._FC.isDrawingMode = false
    render._FC.freeDrawingBrush = undefined
    brush.destroy()
    setBrush(undefined)
  }

  const setEraseMode = (mode: boolean) => {
    if (!brush) return
    setIsEraseMode(mode)
    brush.setErase(mode)
  }

  /**
   * 清除选区
   * */
  const clearSelection = () => {
    if (!render) return
    brush?.clearTargetElement()
  }
  /**
   * 打开套索选区
   * */
  const openLassoBrush = () => {
    const selected = render?._FC.getActiveObject()
    if (!selected || selected._name_ !== ElementName.IMAGE) return
    if (!render?._FC) return
    closeOtherTool()
    const brush = new LassoBrush(render._FC, {
      shapeColor: '#53F6B4',
      shapeContainerName: 'fixText_mask_container', // 承载图形容器名称
      shapeContainerOpacity: 0.3, // 承载图形容器透明度
      showShapeTitle: false, // 是否显示图形标题
      showShapeCloseBtn: false, // 是否显示图形删除按钮
      targetElement: selected as IImage, // 承载图形元素
      maxMaskCount: 3, // 最大承载图形数量
      erase: false, // 是否擦除
      maxBoundsNumber: 512, // 最大承载图形数量
      strokeDashArray: [5, 5],
      isMask: false
    })
    renderStyle?.setCursorStyle({
      defaults: RenderMaskCursor.lassoPlus,
      hover: RenderMaskCursor.lassoPlus,
      move: RenderMaskCursor.lassoPlus,
      mousedown: RenderMaskCursor.lassoPlus,
    })
    render._FC.isDrawingMode = true
    render._FC.freeDrawingBrush = brush
    setBrush(brush)
  }


  /**
   * 打开涂抹选区
   * */
  const openPathBrush = () => {
    const selected = render?._FC.getActiveObject()
    if (!selected || selected._name_ !== ElementName.IMAGE) return
    if (!render?._FC) return
    closeOtherTool()
    const brush = new PathBrush(render._FC, {
      shapeColor: '#53F6B4',
      shapeContainerName: 'fixText_mask_container', // 承载图形容器名称
      shapeContainerOpacity: 0.3, // 承载图形容器透明度
      showShapeTitle: false, // 是否显示图形标题
      showShapeCloseBtn: false, // 是否显示图形删除按钮
      targetElement: selected as IImage, // 承载图形元素
      maxMaskCount: 3, // 最大承载图形数量
      erase: false, // 是否擦除
      maxBoundsNumber: 512, // 最大承载图形数量
      width: 30,
      eraseShapeColor: '#FA99FF',
      eraseShapeOpacity: 0.3,
      isMask: true,
      keepSelected: false
    })
    render._FC.isDrawingMode = true
    render._FC.freeDrawingBrush = brush
    setBrush(brush)
  }

  /**
   * 打开矩形选区
   * */
  const openRectBrush = () => {
    const selected = render?._FC.getActiveObject()
    if (!selected || selected._name_ !== ElementName.IMAGE) return
    if (!render?._FC) return
    closeOtherTool()
    const brush = new RectBrush(render._FC, {
      shapeColor: '#53F6B4',
      shapeContainerName: 'fixText_mask_container', // 承载图形容器名称
      shapeContainerOpacity: 0.3, // 承载图形容器透明度
      showShapeTitle: true, // 是否显示图形标题
      showShapeCloseBtn: true, // 是否显示图形删除按钮
      targetElement: selected as IImage, // 承载图形元素
      maxMaskCount: 3, // 最大承载图形数量
      maxBoundsNumber: 512, // 最大承载图形数量
      erase: false, // 是否擦除
      isMask: false
    })
    render._FC.isDrawingMode = true
    render._FC.freeDrawingBrush = brush
    render._FC.freeDrawingCursor = RenderMaskCursor.rectPlus
    setBrush(brush)
    render._FC.upperCanvasEl.style.cursor = RenderMaskCursor.rectPlus
  }

  const closeFreeDraw = () => {
    if (!render) return
    render._FC.isDrawingMode = false
    render._FC.freeDrawingBrush = undefined
    brush?.destroy()
    setBrush(undefined)
  }

  const exportMask = async () => {
    if (!brush) return
    if (!render?._FC.freeDrawingBrush) return
    const p = await render._FC.freeDrawingBrush.exportShapesToBlob({
      isMerge: true,
      ext: 'png',
      exportContainerType: 'fixText_mask_container',
      backgroundFill: '#000000',
      shapeFill: '#FFFFFF'
    })
    if (!p) return
    let ps: Blob[] = []
    ps = p
    ps.forEach(blob => {
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'fixText_mask.png'
      a.click()
      URL.revokeObjectURL(url)
    })
  }



  const DropDown = (
      <DropdownMenu>
          <DropdownMenuTrigger>
              <Button
                  variant="secondary"
                  className="w-fit"
                  size="icon"
              >
                <FileType className="w-4 h-4" />
              </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={openPathBrush} className="flex items-center gap-2 cursor-pointer">
              <Paintbrush className="w-4 h-4" />
              涂抹工具
            </DropdownMenuItem>
            <DropdownMenuItem onClick={openRectBrush} className="flex items-center gap-2 cursor-pointer">
              <SquareDashed className="w-4 h-4" />
              框选工具
            </DropdownMenuItem>
            <DropdownMenuItem onClick={openLassoBrush} className="flex items-center gap-2 cursor-pointer">
              <LassoSelect className="w-4 h-4" />
              套索工具
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setEraseMode(!isEraseMode)} className="flex items-center gap-2 cursor-pointer">
              <LassoSelect className="w-4 h-4" />
              {isEraseMode ? '关闭擦除' : '开启擦除'}
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center gap-2 cursor-pointer" onClick={clearSelection}>
              <Trash className="w-4 h-4" />
              清除选区
            </DropdownMenuItem>
            <DropdownMenuItem className="flex items-center gap-2 cursor-pointer" onClick={closeFreeDraw}>
              <Trash className="w-4 h-4" />
              关闭工具
            </DropdownMenuItem>
          </DropdownMenuContent>
      </DropdownMenu>
  )

  const DisabledStatus = (
      <Button
          variant="secondary"
          className="w-fit"
          size="icon"
          disabled
      >
        <FileType className="w-4 h-4" />
      </Button>
  )



  return (
    <>
      <WrapperTooltip content="导出改字mask">
        <Button variant="secondary" size="icon" onClick={() => {
            exportMask()
          }} >
          <FileImage className="w-4 h-4" />
        </Button>
      </WrapperTooltip>
      <WrapperTooltip content="">
        {isImage ? DropDown : DisabledStatus}
      </WrapperTooltip>
    </>
  )
}
