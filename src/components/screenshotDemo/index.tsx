import { Button } from '@/components/ui/button';
import { useRenderStore } from '@/store';
import { RenderCutPlugin } from '@/render/plugins/renderCut/plugin';
import { useState } from 'react';
import { Download, Camera, Square, RotateCcw, AlertCircle, Move, ZoomIn } from 'lucide-react';
import { IImage } from '@/render/base/package/image/image';

export default function ScreenshotDemo() {
  const { render } = useRenderStore();
  const [screenshotTool, setScreenshotTool] = useState<RenderCutPlugin | null>(null);
  const [isActive, setIsActive] = useState(false);

  // 启动截图工具
  const startScreenshot = () => {
    if (!render) return;

    // 获取当前选中的对象
    const activeObject = render._FC.getActiveObject();

    // 检查是否选中了图片对象
    if (!activeObject || !(activeObject instanceof IImage)) {
      alert('请先选择一个图片对象');
      return;
    }

    if (screenshotTool) {
      screenshotTool.__destroy__();
    }
    const obs = Object.values(activeObject.aCoords)
    const left = Math.min(...obs.map(item => item.x))
    const right = Math.max(...obs.map(item => item.x))
    const top = Math.min(...obs.map(item => item.y))
    const bottom = Math.max(...obs.map(item => item.y))
    const width = Math.abs(right - left)
    const height = Math.abs(bottom - top)
    try {
      const tool = new RenderCutPlugin(render, {
        target: activeObject as IImage,
        renderMask: true,
        renderMaskColor: '#000000',
        renderMaskOpacity: 0.6,
        maxCutAreaHeight: width,
        maxCutAreaWidth: height,
        minCutAreaWidth: 50,
        minCutAreaHeight: 50,
        cutAreaTemporaryColor: 'red',
        cutAreaTemporaryOpacity: 0,
      });

      setScreenshotTool(tool);
      setIsActive(true);
    } catch (error) {
      console.error('启动截图工具失败:', error);
      alert('启动截图工具失败，请重试');
    }
  };

  // 停止截图工具
  const stopScreenshot = () => {
    if (screenshotTool) {
      screenshotTool.__destroy__();
      setScreenshotTool(null);
      setIsActive(false);
    }
  };

  // 执行截图并下载
  const captureAndDownload = async () => {
    if (!screenshotTool) return;

    try {
      const blob = await screenshotTool.captureScreenshot({
        exportType: 'png',
        quality: 1,
        multiplier: 2,
      });

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `screenshot-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('截图失败:', error);
    }
  };

  // 设置选择框为正方形
  const setSquareSelection = () => {
    if (!screenshotTool) return;

    const bounds = screenshotTool.getSelectionBounds();
    const size = Math.min(bounds.width, bounds.height);

    screenshotTool.setSelectionBox({
      width: size,
      height: size,
      left: bounds.centerX,
      top: bounds.centerY,
    });
  };

  // 测试边界限制
  const testBoundaries = () => {
    if (!screenshotTool) return;

    console.log('=== 边界测试开始 ===');
    console.log('当前选择框边界:', screenshotTool.getSelectionBounds());

    // 测试1: 尝试设置一个超大的选择框
    console.log('测试1: 设置超大选择框 (1000x1000)');
    screenshotTool.setSelectionBox({
      width: 1000,
      height: 1000,
      left: 0,
      top: 0,
    });
    console.log('结果:', screenshotTool.getSelectionBounds());

    // 测试2: 尝试设置一个超小的选择框
    setTimeout(() => {
      console.log('测试2: 设置超小选择框 (10x10)');
      screenshotTool.setSelectionBox({
        width: 10,
        height: 10,
        left: 0,
        top: 0,
      });
      console.log('结果:', screenshotTool.getSelectionBounds());
      console.log('=== 边界测试结束 ===');
    }, 1000);
  };

  // 重置选择框大小
  const resetSelection = () => {
    if (!screenshotTool) return;

    screenshotTool.setSelectionBox({
      width: 300,
      height: 200,
      left: 0,
      top: 0,
    });
  };

  // 检查是否选中了图片
  const hasSelectedImage = () => {
    if (!render) return false;
    const activeObject = render._FC.getActiveObject();
    return activeObject && activeObject instanceof IImage;
  };

  // 获取选择区域信息
  const getSelectionInfo = () => {
    if (!screenshotTool) return;

    const bounds = screenshotTool.getSelectionBounds();
    console.log('选择区域信息:', bounds);
    alert(`选择区域: ${Math.round(bounds.width)}x${Math.round(bounds.height)} 位置: (${Math.round(bounds.left)}, ${Math.round(bounds.top)})`);
  };

  // 居中选择框
  const centerSelection = () => {
    if (!screenshotTool || !render) return;

    const activeObject = render._FC.getActiveObject();
    if (!(activeObject instanceof IImage)) return;

    const imageWidth = activeObject.getScaledWidth();
    const imageHeight = activeObject.getScaledHeight();

    screenshotTool.setSelectionBox({
      width: Math.min(300, imageWidth * 0.6),
      height: Math.min(200, imageHeight * 0.6),
      left: activeObject.left,
      top: activeObject.top,
    });
  };

  // 最大化选择框
  const maximizeSelection = () => {
    if (!screenshotTool || !render) return;

    const activeObject = render._FC.getActiveObject();
    if (!(activeObject instanceof IImage)) return;

    screenshotTool.setSelectionBox({
      width: activeObject.getScaledWidth() - 20,
      height: activeObject.getScaledHeight() - 20,
      left: activeObject.left,
      top: activeObject.top,
    });
  };

  return (
    <div className="flex items-center gap-2 border-l pl-3">
      {!isActive ? (
        <Button
          variant="secondary"
          size="sm"
          onClick={startScreenshot}
          className="flex items-center gap-2"
          disabled={!hasSelectedImage()}
        >
          <Camera className="h-4 w-4" />
          {hasSelectedImage() ? '截图工具' : '请先选择图片'}
          {!hasSelectedImage() && <AlertCircle className="h-4 w-4 ml-1" />}
        </Button>
      ) : (
        <>
          <Button
            variant="secondary"
            size="sm"
            onClick={captureAndDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            下载截图
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={setSquareSelection}
            className="flex items-center gap-2"
          >
            <Square className="h-4 w-4" />
            正方形
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={centerSelection}
            className="flex items-center gap-2"
          >
            <Move className="h-4 w-4" />
            居中
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={maximizeSelection}
            className="flex items-center gap-2"
          >
            <ZoomIn className="h-4 w-4" />
            最大化
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={getSelectionInfo}
            className="flex items-center gap-2"
          >
            信息
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={resetSelection}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            重置
          </Button>

          {process.env.NODE_ENV === 'development' && (
            <Button
              variant="outline"
              size="sm"
              onClick={testBoundaries}
              className="flex items-center gap-2"
            >
              测试边界
            </Button>
          )}

          <Button
            variant="destructive"
            size="sm"
            onClick={stopScreenshot}
          >
            退出截图
          </Button>
        </>
      )}
    </div>
  );
}
