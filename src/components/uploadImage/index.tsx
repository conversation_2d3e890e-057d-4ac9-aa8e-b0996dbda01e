import { Upload as ArcoUpload } from '@arco-design/web-react';
import { Button } from '@/components/ui/button';
import { Image } from 'lucide-react';
import file2base64 from '@/utils/file2base64';
import { useRenderStore } from '@/store/render';
import { createImage } from '@/render';
import Upload from '@/hooks/uploader';
export default function UploadImage() {
    const { render } =
    useRenderStore();
    const handleImageChange = async (file: File) => {
        if (render?._FC) {
          const base64 = await file2base64(file);

          const result = await Upload(file, 'photo')
            console.log(result)
          const el = await createImage('', {
            src: result,
          });
          render?.addToViewPortCenter(el);
          render?._FC.fire('object:insert', {
            objects: [el],
          });
        }
      };
    return (
        <ArcoUpload
            // autoUpload={false}
            imagePreview={false}
            showUploadList={false}
            customRequest={async ({ file, onSuccess, onError }) => {
                try {
                    await handleImageChange(file)
                    onSuccess()
                } catch (error) {
                    onError()
                }
            }}
        >
            <Button variant="secondary" size="icon" className="relative">
                <Image className="h-5 w-5" />
            </Button>
        </ArcoUpload>
    )
}
