import { useEffect, useRef, useState } from "react";
import { Button } from "../ui/button";
import { FixImagePlugin, IImage } from "@/render";
import { useRenderStore } from "@/store";

export default function FixTextComponent() {
    const { render } = useRenderStore();
    const fixPluginRef = useRef<FixImagePlugin | null>(null)
    const imageRef = useRef<IImage | null>(null)
    const [disabled, setDisabled] = useState(true)
    const [isFixing, setIsFixing] = useState(false)
    const initFixPlugin = () => {
        if (!render) return;
        if (!imageRef.current) return;
        fixPluginRef.current = new FixImagePlugin(render, {
            maxFixAreaWidth: 1024,
            maxFixAreaHeight: 1024,
            minFixAreaWidth: 10,
            minFixAreaHeight: 10,
            maxAreaCount: 1,
            fixAreaTemporaryColor: 'red',
            fixAreaTemporaryOpacity: 0.5,
            renderMask: true,
            renderMaskColor: 'red',
            renderMaskOpacity: 0.5,
            target: imageRef.current,
        })
        render.use(fixPluginRef.current)
        setIsFixing(true)
    }

    const unUseFixPlugin = () => {
        if (!isFixing) return
        if (!fixPluginRef.current) return
        render?.unUse(fixPluginRef.current?.__name__)
        setIsFixing(false)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const selectHandler = () => {
        imageRef.current = null;
        setDisabled(true)
        if (!render) return;
        const activeObject = render._FC.getActiveObject()
        if (!activeObject) return;
        if (activeObject._name_ !== 'image') return;
        imageRef.current = activeObject as IImage;
        setDisabled(false)
    }
    useEffect(() => {
        if (!render) return;
        render._FC.on('selection:created', selectHandler)
        render._FC.on('selection:updated', selectHandler)
        render._FC.on('selection:cleared', selectHandler)
        return () => {
            render._FC.off('selection:created', selectHandler)
            render._FC.off('selection:updated', selectHandler)  
            render._FC.off('selection:cleared', selectHandler)
        }
    }, [render, selectHandler])
    const exportFixMask = () => {
        if (!fixPluginRef.current) return;
        fixPluginRef.current.exportBlob({
            backgroundColor: '#000000',
            fixAreaColor: '#FFFFFF',
            format: 'png',
            quality: 1
        }).then(blob => {
            const url = URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = 'fix_mask.png'
            a.click()
            URL.revokeObjectURL(url)
        })
    }
    return (
        <>
        <Button disabled={disabled} onClick={() => {
        if (isFixing) {
            unUseFixPlugin()
        } else {
            initFixPlugin()
        }
    }}>
        {isFixing ? '取消' : 'fix text'}
    </Button>
    <Button disabled={!isFixing} onClick={() => {
        exportFixMask()
    }}>
        导出改字mask
    </Button>
        </>
    )
}