import { Uploader, UploadEnv } from '@meitu/upload'

const uploader = new Uploader({
  debug: false,
  env: UploadEnv.Test,
})

export default async function Upload (file: string | File, type: 'photo' | 'video') { 
        try {
            const result = await uploader.upload(file, {
                app: 'whee',
                type,
            })
            return result.url
        } catch (error) {
            console.error(error)
        }
    }

    