import {
  Ren<PERSON>,
  <PERSON><PERSON><PERSON>,
  MouseAction,
  Selection,
  HistoryPlugins,
  ContextMenu,
  RenderStyle,
  FabricObject,
  ElementName,
  ScaleTheme,
  GuideLine, WheeRotateTheme,
} from '@/render';
import { useUiStateStore, useRenderStore } from '@/store';
import { useCallback, useEffect, useState } from 'react';
import ContextMenuComponent from '@/components/contextMenu';

export const useRender = (container: string) => {
  const [renderHotKey] = useState<HotKey | null>(null);
  const [renderMouseAction] =
    useState<MouseAction | null>(null);
  const { setIsCreateFrame, setIsCreateText, setZoom } = useUiStateStore();
  const {
    setRender,
    render,
    setRenderSelection,
    renderSelection,
    setHistoryPlugins,
    historyPlugins,
    setContextMenu,
    setRenderStyle,
  } = useRenderStore();

  const addFrame = useCallback(() => {
    setIsCreateFrame(false);
  }, [setIsCreateFrame]);


  const addText = useCallback(() => {
    setIsCreateText(false);
  }, [setIsCreateText]);
  const handleZoom = useCallback((event: any) => {
    setZoom(Math.floor(event.zoom * 100));
  }, [setZoom]);
  const handleAdd = useCallback(({ target }: { target: FabricObject }) => {
    if (target._name_ === ElementName.FRAME) {
      setIsCreateFrame(false);
    } else if (target._name_ === ElementName.TEXT) {
      setIsCreateText(false);
    }
  }, [setIsCreateFrame, setIsCreateText]);
  useEffect(() => {
    const render = new Render({
      container: container,
      maxImageCount: 1,
      maxZoom: 4,
      minZoom: 0.03,
    });
    setRender(render);
    const selection = new Selection(render, {
      text: {
        text: 'Hello',
      }
    });
    const historyPlugins = new HistoryPlugins(render, {
      historyConfig: {
        isDisabledSelect: true,
        max: 100,
        isDisabledMask: true,
      }
    });
    const contextMenuPlugin = new ContextMenu(render, ContextMenuComponent, {});
    const renderStyle = new RenderStyle(render, {
      basic: {
        textureSize: 10000,
      },
      // rotate: [{
      //   key: 'mbm',
      //   show: true,
      //   shape: 'ellipse',
      //   xSize: 26,
      //   ySize: 26,
      //   offsetX: 0,
      //   offsetY: 13 + 8,
      //   backgroundColor: '#FFFFFF',
      //   icon,
      //   apply: [ElementName.IMAGE, ElementName.VIDEO, ElementName.CONTAINER],
      //   cursor: (
      //     _eventData: TPointerEvent,
      //     _control: Control,
      //     fabricObject: InteractiveFabricObject,
      // ) => {
      //     console.log(_eventData, fabricObject)
      //     return `url(${ROTATE_CURSOR}) 16 16, auto`
      //   },
      // }],
      rotate: WheeRotateTheme,
      scale: ScaleTheme,
      frame: {
        maxWidth: 500,
        maxHeight: 500,
        minWidth: 64,
        minHeight: 64,
      },
      selection: {
        defaultSelectionColor: '#F761FF',
        defaultMultiSelectionColor: '#F761FF',
        defaultFrameSelectionColor: '#F761FF',
        defaultImageSelectionColor: '#F761FF',
        defaultTextSelectionColor: '#F761FF',
        defaultContainerSelectionColor: '#F761FF',
        borderWidth: 2,
      }
    });
    const mouseAction = new MouseAction(render);
    new GuideLine(render._FC, {
      enableScaleAdsorption: false,
    })

    setRenderSelection(selection);
    setHistoryPlugins(historyPlugins);
    setContextMenu(contextMenuPlugin);
    setRenderStyle(renderStyle);
    render
      .use(selection)
      .use(historyPlugins)
      .use(renderStyle)
      .use(contextMenuPlugin)
      .use(mouseAction)


    render._FC.on('viewport:zoom', handleZoom);
    render._FC.on('object:added', handleAdd);
    return () => {
      render._FC.off('viewport:zoom', handleZoom);
      render._FC.off('object:added', handleAdd);
      render.unmount();
    };
  }, [container, addFrame, addText, setRender, setRenderSelection, setHistoryPlugins, setContextMenu, setRenderStyle, setZoom, handleZoom, handleAdd]);

  return {
    render,
    renderHotKey,
    renderMouseAction,
    renderSelection,
    historyPlugins,
  };
};
