{"name": "@meitu/whee-infinite-canvas", "version": "1.0.73-20.beta", "type": "module", "license": "UNLICENSED", "files": ["lib", "worker"], "main": "./lib/render.es.js", "module": "./lib/render.es.js", "types": "./lib/render.d.ts", "exports": {".": {"import": "./lib/render.es.js", "types": "./lib/render.d.ts"}, "./worker": {"import": "./worker/workers.iife.js", "types": "./worker/workers.d.ts"}}, "peerDependencies": {"fabric": "^6.0.0", "react": "^18.0.0", "react-dom": "^18.0.0"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "lib": "vite build --config vite.lib.config.ts", "worker": "vite build --config vite.worker.ts", "build:all": "npm run lib && npm run worker"}, "dependencies": {"@arco-design/web-react": "^2.66.1", "@meitu/upload": "^4.8.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.80.6", "@velipso/polybool": "^2.0.11", "await-to-js": "^3.0.0", "axios": "^1.9.0", "canvaskit-wasm": "^0.39.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "es-toolkit": "^1.39.8", "events": "^3.3.0", "fabric": "^6.0.0", "gifuct-js": "^2.1.2", "hotkeys-js": "^3.13.9", "idb": "^8.0.3", "jss": "^10.10.0", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "lucide-react": "^0.469.0", "nanoid": "^5.0.9", "primereact": "^10.9.5", "react": "^18.0.0", "react-dom": "^18.0.0", "reset.css": "^2.0.2", "svg64": "^2.0.0", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vite-plugin-dts": "^4.4.0", "workerpool": "^9.2.0", "zod": "^3.25.20", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/events": "^3.0.3", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "i": "^0.3.7", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.3.5"}}