import { defineConfig } from 'vite'
import path from "path"
import dts from 'vite-plugin-dts'

export default defineConfig({
  plugins: [
    dts({
      outDir: './worker',
      include: ['src/render/workers/**/*'],
      exclude: ['src/**/*.test.ts', 'src/**/*.spec.ts'],
    })
  ],
  build: {
    lib: {
      entry: {
        workers: path.resolve(__dirname, './src/render/workers/index.ts'),
      },
      fileName: (format, entryName) => {
        return `${entryName}.${format}.js`;
      },
      name: 'workers',
      formats: ['iife'],
    },
    outDir: './worker',
  },
})
