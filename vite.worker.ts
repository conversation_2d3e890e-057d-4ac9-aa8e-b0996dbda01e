import { defineConfig } from 'vite'
import path from "path"
import dts from 'vite-plugin-dts'

// 获取要构建的worker类型
const workerType = process.env.WORKER_TYPE || 'image'

const entryMap = {
  'image': {
    entry: path.resolve(__dirname, './src/render/workers/index.ts'),
    name: 'ImageWorker'
  },
  'gif': {
    entry: path.resolve(__dirname, './src/render/workers/gif-worker.ts'),
    name: 'GifWorker'
  }
}

const config = entryMap[workerType as keyof typeof entryMap]

export default defineConfig({
  plugins: [
    dts({
      outDir: './worker',
      include: ['src/render/workers/**/*'],
      exclude: ['src/**/*.test.ts', 'src/**/*.spec.ts'],
    })
  ],
  build: {
    lib: {
      entry: config.entry,
      fileName: () => {
        return `${workerType}-worker.iife.js`;
      },
      name: config.name,
      formats: ['iife'],
    },
    outDir: './worker',
  },
})
